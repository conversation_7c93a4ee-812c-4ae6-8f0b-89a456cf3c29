package de.adesso.fischereiregister.card_orders.services;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import de.adesso.fischereiregister.card_orders.config.CardOrderConfig;
import de.adesso.fischereiregister.card_orders.ports.HashingPort;
import de.adesso.fischereiregister.core.model.Address;
import de.adesso.fischereiregister.core.model.FishingLicense;
import de.adesso.fischereiregister.core.model.IdentificationDocument;
import de.adesso.fischereiregister.core.model.Person;
import de.adesso.fischereiregister.core.model.type.FederalState;
import de.adesso.fischereiregister.core.model.type.LicenseType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpStatusCode;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.web.reactive.function.client.ClientResponse;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;


@Service
@Slf4j
public class HttpOrderServiceImpl implements HttpOrderService {

    private final CardOrderConfig cardOrderConfig;
    private final ObjectMapper objectMapper;
    private final HashingPort hashingPort;
    private final WebClient webClient;

    public HttpOrderServiceImpl(CardOrderConfig cardOrderConfig, ObjectMapper objectMapper, HashingPort hashingPort, @Qualifier("orderWebClient") WebClient webClient) {
        this.cardOrderConfig = cardOrderConfig;
        this.objectMapper = objectMapper;
        this.hashingPort = hashingPort;
        this.webClient = webClient;
    }

    /*
            Calls a service to place an Order for a Card with following arguments:

            String orderId,
            String fishingLicenseId,
            LicenseType type,
            String issueDate,
            String expirationDate,
            String federalStateAbbreviation,
            String issuedByAddress,
            String title,
            String givenNames,
            String surname,
            String birthdate,
            String birthplace,
            String street,
            String streetNumber,
            String zipcode,
            String city,
            String template,
            String payload
         */
    @Override
    public void registerOrder(
            UUID orderId,
            LocalDate issueDate,
            RegisterOrderInformation registerOrderInformation,
            String salt,
            String issuedByOfficeAddress) {

        FishingLicense fishingLicense = registerOrderInformation.fishingLicense();
        Person person = registerOrderInformation.person();
        UUID registerId = registerOrderInformation.registerId();
        IdentificationDocument cardDocument = registerOrderInformation.identificationDocument();

        validateData(issueDate, fishingLicense, person, registerId);

        FederalState federalState = fishingLicense.getIssuingFederalState();
        Address address = person.getAddress() != null ? person.getAddress() : person.getOfficeAddress();

        LicenseType licenseType = fishingLicense.getType();
        OrderRequestLicenseType openApiType = OrderRequestLicenseTypeMapper.INSTANCE.toOrderRequestLicenseType(licenseType);
        String template = determineTemplate(licenseType, federalState);

        String payload = hashingPort.getQROrNFCDataForLicense(registerId, fishingLicense.getNumber(), person, cardDocument.getDocumentId(), salt);

        OrderRequest request = new OrderRequest(
                orderId.toString(),
                fishingLicense.getNumber(),
                cardDocument.getDocumentId(),
                openApiType,
                issueDate.format(DateTimeFormatter.ofPattern("dd.MM.yyyy")),
                null,
                federalState,
                issuedByOfficeAddress,
                person.getTitle(),
                person.getFirstname(),
                person.getLastname(),
                person.getBirthdate().toString(),
                person.getBirthplace(),
                address.getStreet(),
                address.getStreetNumber(),
                address.getPostcode(),
                address.getCity(),
                template,
                payload
        );
        if (cardOrderConfig.isEnabled()) {
            placeOrder(request);
        } else {
            log.info("HTTP Card order is disabled via config. Sending order request to log instead.");
            logOrder(request);
        }
    }

    private void validateData(LocalDate issueDate, FishingLicense fishingLicense, Person person, UUID registerId) {
        List<OrderValidationError> orderValidationErrors = new ArrayList<>();

        if (issueDate == null) {
            orderValidationErrors.add(new OrderValidationError("Issue Date is not allowed to be null in Order Service for Register id: " + registerId));
        }

        orderValidationErrors.addAll(validateLicense(fishingLicense, registerId));

        orderValidationErrors.addAll(validatePerson(person, registerId));
        if (person != null) {
            Address address = person.getAddress() != null ? person.getAddress() : person.getOfficeAddress();
            orderValidationErrors.addAll(validateAddress(address, registerId));
        }

        throwExceptionForValidationErrors(orderValidationErrors);
    }

    private List<OrderValidationError> validateLicense(FishingLicense fishingLicense, UUID registerId) {
        List<OrderValidationError> licenseValidationErrors = new ArrayList<>();

        if (fishingLicense == null) {
            licenseValidationErrors.add(new OrderValidationError("fishingLicense is not allowed to be null in Order Service for Register id: " + registerId));
        } else {
            if (fishingLicense.getIssuingFederalState() == null) {
                licenseValidationErrors.add(new OrderValidationError("fishingLicense issuing federal state is not allowed to be null in Order Service for Register id: " + registerId));
            }
            if (fishingLicense.getNumber() == null) {
                licenseValidationErrors.add(new OrderValidationError("fishingLicense Number is not allowed to be null in Order Service for Register id: " + registerId));
            }
            if (fishingLicense.getType() == null ||
                    (fishingLicense.getType() != LicenseType.REGULAR
                            && fishingLicense.getType() != LicenseType.LIMITED
                            && fishingLicense.getType() != LicenseType.VACATION)) {
                licenseValidationErrors.add(new OrderValidationError("No supported Fishing License Type " + fishingLicense.getType() + " given to Order Service for Register Id: " + registerId));
            }
        }

        return licenseValidationErrors;
    }

    private List<OrderValidationError> validatePerson(Person person, UUID registerId) {
        List<OrderValidationError> personValidationErrors = new ArrayList<>();
        if (person == null) {
            personValidationErrors.add(new OrderValidationError("person is not allowed to be null in Order Service for Register id: " + registerId));
        } else {
            if (person.getFirstname() == null) {
                personValidationErrors.add(new OrderValidationError("firstname/givennames is not allowed to be null in Order Service for Register id: " + registerId));
            }
            if (person.getLastname() == null) {
                personValidationErrors.add(new OrderValidationError("lastname/surename is not allowed to be null in Order Service for Register id: " + registerId));
            }
            if (person.getBirthdate() == null) {
                personValidationErrors.add(new OrderValidationError("birthdate is not allowed to be null in Order Service for Register id: " + registerId));
            }
            if (person.getBirthplace() == null) {
                personValidationErrors.add(new OrderValidationError("birthplace is not allowed to be null in Order Service for Register id: " + registerId));
            }
        }
        return personValidationErrors;
    }

    private List<OrderValidationError> validateAddress(Address address, UUID registerId) {
        List<OrderValidationError> addressValidationErrors = new ArrayList<>();

        if (address == null) {
            addressValidationErrors.add(new OrderValidationError("address is not allowed to be null in Order Service for Register id: " + registerId));
        } else {
            if (address.getStreet() == null) {
                addressValidationErrors.add(new OrderValidationError("street is not allowed to be null in Order Service for Register id: " + registerId));
            }
            if (address.getStreetNumber() == null) {
                addressValidationErrors.add(new OrderValidationError("streetnumber is not allowed to be null in Order Service for Register id: " + registerId));
            }
            if (address.getPostcode() == null) {
                addressValidationErrors.add(new OrderValidationError("zipcode/postcode is not allowed to be null in Order Service for Register id: " + registerId));
            }
            if (address.getCity() == null) {
                addressValidationErrors.add(new OrderValidationError("city is not allowed to be null in Order Service for Register id: " + registerId));
            }
        }

        return addressValidationErrors;
    }

    private void throwExceptionForValidationErrors(List<OrderValidationError> orderValidationErrors) {
        StringBuilder errors = new StringBuilder();
        orderValidationErrors.stream().map(OrderValidationError::getMessage).forEach(errors::append);
        if (!errors.isEmpty()) {
            throw new IllegalStateException(errors.toString());
        }
    }


    /**
     * Layout eg: A_SH_v1
     * <p>
     * layout composition: license-type_federal-state_template-version
     * <p>
     * version is taken from application*.yaml
     */
    private String determineTemplate(LicenseType openApiType, FederalState federalState) {
        return openApiType.toString() + "_" + federalState.toString() + "_" + cardOrderConfig.getTemplateVersion();
    }

    private void logOrder(OrderRequest orderRequest) {
        final ObjectWriter ow = objectMapper.writer().withDefaultPrettyPrinter();
        try {
            final String orderString = ow.writeValueAsString(orderRequest);
            log.info("Order Request: {}", orderString);
        } catch (JsonProcessingException e) {
            log.error("cannot output request Information : {}", e.getMessage());
        }
    }

    private void placeOrder(OrderRequest request) {
        webClient.post()
                .uri(cardOrderConfig.getEndpointPath())
                .contentType(MediaType.APPLICATION_JSON)
                .bodyValue(request)
                .retrieve()
                .onStatus(
                        HttpStatusCode::isError,
                        this::handleError)
                .bodyToMono(String.class)
                .doOnError(e -> log.error("Order sending failed" + " - " + e.getMessage(), e))
                .subscribe(result -> log.info("Order sent successfully" + " - " + result));
    }

    private Mono<Throwable> handleError(ClientResponse response) {
        return response.bodyToMono(String.class)
                .flatMap(errorBody -> {
                    String message = "Error occurred: " + response.statusCode();
                    log.error(message + " - Details: " + errorBody);
                    return Mono.error(new RuntimeException("Error: " + errorBody));
                });
    }

}
