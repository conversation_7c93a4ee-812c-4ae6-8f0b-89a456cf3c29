:jbake-title: Einführung und Ziele
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 1
:filename: /chapters/01_introduction_and_goals.adoc
ifndef::imagesdir[:imagesdir: ../../images]
ifndef::c4dir[:c4dir: ../structurizr/diagrams]
ifdef::env-vscode[]
:c4dir: ../../structurizr/diagrams
:source-highlighter: highlight.js
endif::[]

:toc:



[[section-introduction-and-goals]]
==	Einführung und Ziele

Die derzeitigen Verwaltungsprozesse im Fischereiwesen sind dezentral organisiert und werden überwiegend analog durch Ordnungsämter, Fischereibehörden, hoheitlich beliehene Verbände sowie Kreisbehörden abgewickelt. Der geringe Digitalisierungsgrad führt zu langen Bearbeitungszeiten und individuellen, manuellen Prozessen. Zudem gibt es keine einheitlichen Rechtsgrundlagen zwischen den Bundesländern, was zu unterschiedlichen Verwaltungsprozessen und Rahmenbedingungen führt. Diese Diskrepanzen beeinträchtigen die Gültigkeit und Fälschungssicherheit der Fischereischeine sowie die Anerkennung von Fischereischeinprüfungen.

Das Projekt zielt darauf ab, die Verwaltungsleistungen im Fischereiwesen durch die Entwicklung eines Fachverfahrens und eines Registers zu digitalisieren. Die Digitalisierung soll die Prozesse weitestgehend automatisieren und zentralisieren. Betroffene Prozesse umfassen unter anderem die Ausstellung von Fischereischeinen und Prüfungszeugnissen, den Nachweis der Fischereiabgabe, die Ausstellung von Sonder-Fischereischeinen sowie die Kontrolle und Entziehung von Fischereischeinen. Durch diese Maßnahmen sollen die Effizienz gesteigert, die Bearbeitungszeiten verkürzt und die Prozesssicherheit erhöht werden.

[[section-introduction-and-goals-task]]
=== Aufgabenstellung

Entwicklung einer Softwarelösung um das Fachverfahren und Register Fischereiwesen mit seinem wichtigsten Verwaltungsleistungen zu digitalisieren.

Folgende Verwaltungsleistungen sollen abgebildet werden:

- Fischereischein-Prüfungszeugnis ausstellen
- Fischereischein ausstellen (digital oder analog)
- Umtausch alter Fischereischeine vornehmen
- Nachweis der Fischereiabgabe erstellen
- Urlauber-/Ausländer-Fischereischein ausstellen oder verlängern (digital oder analog)
- Sonder-Fischereischein für Menschen mit Behinderung ausstellen
- Entzug eines Fischereischeins (Sperre des Besitzers)
- Kontrolle von Fischereischeinen

Die Softwarelösung muss <<Mehrmandantenfähig>> sein, da sie von mehreren Bundesländern eingesetzt werden soll. Dieses Projekt steht im Zusammenhang mit dem externen Projekt <<OZG-EfA-Fischereiprojekt>> welches die Fischereiverwaltung für die Bürger als Online-Dienst ermöglicht.

Treibekräfte des Projektes sind Ministeriums für Landwirtschaft, ländliche Räume, Europa und Verbraucherschutz Schleswig-Holstein <<MLLEV>> und das Onlinezuganggesetz (<<OZG>>), genauer das "Einer für Alle"-Prinzip (<<EfA>>) aus dem Gesetz.

Als Fachlicher Interessensvertreter wurde die Dataport von der <<MLLEV>> (Endkunde) benannt. Die Entwicklung und der Betrieb wird durch Dataport sichergestellt.

[[section-introduction-and-goals-qualitygoals]]
=== Qualitätsziele

Die folgende Tabelle beschreibt die zentralen Qualitätsziele von _DigiFischDok_, wobei die Reihenfolge eine grobe Orientierung bezüglich der Wichtigkeit vorgibt.

[cols="1,1,1" options="header"]
|===
|Id |Qualitätsziel |Motivation und Erläuterung

// https://quality.arc42.org/qualities/performance-efficiency
|[[QG-1,QG-1]]QG-1
|Leistungsfähigkeit
|Es müssen 300.000 Fischereischeine verwaltet werden können.

|[[QG-2,QG-2]]QG-2
|Inklusivität
|Barrierefreiheit muss gewährleistet sein.

|[[QG-3,QG-3]]QG-3
// https://quality.arc42.org/qualities/interaction-capability
// https://quality.arc42.org/qualities/user-engagement
// - Analog Motivation, Mehr Zeit für andere Aufgaben, Einfachere Bearbeitung, übersichtliches Verfahren, Benutzerfreundliches Fachverfahren, effiziente Verfahren, weniger Antragsteller vor Ort, Rechtssicherheit, automatisierte und einfache Auswertung
|User engagement
|Die Benutzeroberflächen müssen benutzerfreundlich und effizent sein. Die Benutzer sollen durch das neue System Arbeitszeit sparen wenn sie im Fachverfahren arbeiten.

|[[QG-4,QG-4]]QG-4
// https://quality.arc42.org/qualities/reusability
// - Das Fachverfahren ist das Verwaltungssystem des Registers von Schleswig-Holstein, jedoch mit klarer Ausrichtung auf die Nachnutzbarkeit in allen interessierten Bundesländern.
|Reusability
|Eine Nachnutzbarkeit durch andere Bunderländer in Deutschland muss möglich sein. 

|[[QG-5,QG-5]]QG-5
// https://quality.arc42.org/qualities/adaptability
// - Alle denkbaren Synergien werden ausgeschöpft, wenn möglichst alle Bundesländer perspektivisch an dieser Lösung teilnehmen.
// - Bei der Konzeptionierung soll eine Nachnutzbarkeit des Fachverfahrens dahingehend berücksichtigt werden, dass das Fachverfahren in einer zweiten Ausbaustufe durch andere Bundesländer (unabhängig von der Tatsache, ob es sich um ein Dataport-Trägerland handelt oder nicht) erworben und genutzt werden kann.
// In diesem Lastenheft wird vom Register gesprochen als ein System, das Daten enthält, die bundeslandübergreifend verfügbar sein müssen.
|Adaptability
|Anpassungen durch Gesetzänderungen oder Unterschiede in Bundesländer im Fischereifachverfahren muss die Software ermöglichen.

|[[QG-6,QG-6]]QG-6
// https://quality.arc42.org/qualities/analysability
// - Analog Motivation, Mehr Zeit für andere Aufgaben, Einfachere Bearbeitung, übersichtliches Verfahren, Benutzerfreundliches Fachverfahren, effiziente Verfahren, weniger Antragsteller vor Ort, Rechtssicherheit, automatisierte und einfache Auswertung
|Analysability
|Die Softwarelösung muss eine automatisierte und einfache Auswertung der gespeicherten Daten ermöglichen.


|[[QG-7,QG-7]]QG-7
// 
|Datensicherheit
|Nur Berechtigte Personen haben zurgiff auf die Daten, und auch nur auf die Daten auf die sie zugreifen dürfen.

|[[QG-8,QG-8]]QG-8
// 
|Revisionssicherheit
|Alle Änderungen an die Daten müssen protokolliert werden.

|[[QG-9,QG-9]]QG-9
// https://quality.arc42.org/qualities/authenticity
|Authenticity
|Es muss möglichsein das Benutzer unterschiedliche Rollen und Rechte zu vergeben, und sie müssen sich eindeutig identizieren können.

|===

Die <<section-quality-scenarios, Qualitätsszenarien in Abschnitt 10>> konkretisieren diese Qualitätsziele und dienen insbesondere auch dazu ihre Erreichung zu bewerten.

[[section-introduction-and-goals-stakeholder]]
=== Stakeholder

.Projektorganisation zwischen Dataport und adesso
image::01-Stakeholder-Projektorganisation.png[01-Stakeholder-Projektorganisation,400]

Zur Kommunikation mit dem Auftraggeber stellt Dataport einen Brückenkopf, der zur Klärung von Detailfragen ausschließlich mit dem adesso Brückenkopf kommuniziert und Berichte vom adesso Brückenkopf erhält.
Die Kommunikation mit dem adesso-Team erfolgt hierbei ausschließlich über den adesso Brückenkopf.

Eine direkte Kommunikation mit dem Endkunden erfolgt im Zuge dieses Projektes einzig über den Brückenkopf von Dataport .

[cols="2,1,1,3" options="header"]
|===
|Rolle |Kontakt |Firma |Erwartungshaltung an die Architektur u. Dokumentation

|Projektmanager
|Michael Bartoli
|adesso
|_<Erwartung-1>_

|Projektmanager
|Jan-Phillip Staack
|adesso
|_<Erwartung-2>_

|Softwareentwicklungsteam
|
|adesso
a| Aufgabe der Softwarelösung, Aufbau der Softwarelösung, Bestandteile und Schnittstellen der Softwarelösung, Ansprechpartner bei Fragen zu kennen, Eingesetzte Technologien, Einschränkungen die, die Architektur oder die Entwicklung beeinflussen.

|Projektleitung
|Mareike Much
|Dataport
|_<Erwartung-3>_

|Produktverantwortlicher
|Katrin Ameskamp
|Dataport
|_<Erwartung-4>_

|Bedarfsträger Fischereireferent (Fachverantwortung)
|Dr. Roland Lemcke
|MLLEV
|_<Erwartung-4>_

|===
