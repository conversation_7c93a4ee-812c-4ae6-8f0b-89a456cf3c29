:jbake-title: Lösungsstrategie
:jbake-type: page_toc
:jbake-status: published
:jbake-menu: arc42
:jbake-order: 4
:filename: /chapters/04_solution_strategy.adoc
ifndef::imagesdir[:imagesdir: ../../images]
ifndef::c4dir[:c4dir: ../structurizr/diagrams]
ifdef::env-vscode[]
:c4dir: ../../structurizr/diagrams
:source-highlighter: highlight.js
endif::[]

:toc:



[[section-solution-strategy]]
== Lösungsstrategie

Die Gesamtlösung ist in verschiedene Bausteine aufgeteilt. Deren Funktion, die wesentliche Interaktion untereinander sowie die Schnittstellen nach außen werden im Folgenden genauer beschrieben. Eine abstrakte Übersicht gibt nachfolgend Abbildung:

plantuml::{imagesdir}/solution.puml["structurizr-SystemContext",format=svg,width=400]

=== Register

Die Komponente Register beinhaltet die Subdomänen Bürger*innen Daten, Bundesland, Dokumente, Reporting und Prüfungsimport. Es stellt die Backendlogik des Systems dar. Dieses wird vom „Fachverfahren“ und der Kontroll-App über REST aufgerufen. Zu Authentisierungs- und Autorisierungszwecken wird das Register das IAM-System anfragen. Die Backendteile der Subdomänen Fachverfahren und Kontroll-App gehören somit ebenfalls zur technischen Komponente Register.

Das Register bietet die folgenden externen Schnittstellen an:

 * Online-Dienste: Eine EfA-konforme OD-Schnittstelle für die Fischerei-Online-Dienste

 *  Prüfungsimport: Eine noch in Zusammenarbeit mit einem Parallelprojekt in Bayern zu definierende Schnittstelle für die Übergabe von Prüfungsergebnissen (siehe UC 2a). adesso gibt die Definition der REST-Schnittstelle für diesen Zweck vor.

 * XÖV: Eine XÖV konforme Schnittstelle für andere behördliche Verfahren. Eine für das Fischereiverfahren passende X-Schnittstelle muss erst noch von einer anderen Partei außerhalb des hier definierten Angebotes definiert werden. Diese Schnittstelle ist daher nicht Teil dieses Angebotes.

Das Register bindet folgende ausgehende (externen) Schnittstellen an:

 * Service Konto: Das Register wird Dokumente in das OSI-Postfach der Bürger*innen ablegen.

Dazu wird die OSI-Schnittstelle angebunden.

 * Kartendruckdienstleister: Druckaufträge für Fischereischeinkarten sollen elektronisch an einen Dienstleister übermittelt werden können. Der Dienstleister ist noch nicht ausgewählt. Es ist zum Zeitpunkt des Angebotes noch unklar, ob Qualität und Nutzbarkeit der Schnittstelle mit den Anforderungen des Projektes übereinstimmen, daher kann adesso hierzu keine Aussage treffen.
 
 * Kartendruckdienstleister Statusupdates: Der Kartendruckdienstleister liefert über eine REST-Schnittstelle Status-Updates zum Herstellungs- und Versandstatus der Karten.

Die für das Register gewählte Architektur entspricht dem CQRS/ES-Pattern. Kernpunkt ist dabei eine im Kern eventgetriebene Datenhaltung (Event-Sourcing, ES). Alle Änderungen an den Daten werden als Events persistiert, aus denen die verschiedenen, benötigten Daten-Ansichten generiert werden. Der eventgetriebene Datenhaltung ist dabei inhärent eigen, dass revisionistische Anforderungen an die Datenhaltung automatisch eingehalten werden und unumgänglich sind. Der persistierte Eventstrom entspricht einem vollständigen Audit-Log. Ein Abweichen der von Datenquelle und Audit-Log ist technisch nicht möglich, da es sich um dieselbe Datenquelle handelt. [<<QG-8>>]

Auch wird die Datensicherheit erhöht, weil die Speicherung als append-only passiert. Es werden nur Daten hinzugefügt, eine Löschung ist im Normalbetrieb nicht vorgesehen. Für die Implementierung von Event-Sourcing hat sich das CQRS-Pattern als sinnvolles Companion-Pattern erwiesen. Die beiden Pattern ergänzen sich, um das System effizient und weiterentwickelbar zu gestalten. Bei CQRS erfolgt eine Separierung von Schreib-Datenmodell und einem oder mehreren Lese-Datenmodellen. Die Verbindung zwischen Schreib- und Lese-Datenmodell erfolgt eventgerieben. Für die Kernanwendung ist es mit diesem Pattern möglich, den jeweiligen spezifischen Anforderungen an die jeweiligen Schnittstellen ohne wesentlichen Mehraufwand bei der Programmierung gerecht zu werden.

Die Implementierung des CQRS/ES-Patterns erfolgt unter Nutzung des Axon-Frameworks. Daraus folgt eine Nutzung von Java, mit der Entscheidung das SpringBoot-Framework für die Umsetzung zu nutzen.

//TODO Querreferenzierung der Qualitätsziele. Beispiel im Text: "da es sich um dieselbe Datenquelle handelt. [QG-8]"; Evtl. kannst du das noch verlinken.

==== Events

Zentraler Aspekt einer CQRS/ES-Architektur sind die persistierten Events, welche in der nachfolgenden Grafik dargestellt werden.

//Todo Grafik einfügen aus Kapitel 7, Seite 14

//Todo Reste von Kapitel 7 aus dem Architekturdokumentation_v2.docx (das an dem wir bis letzte Woche gearbeitet haben) einfügen.

==== Datenmodel (Write Model)

//Todo Kapitel 7.1 aus dem Architekturdokumentation_v2.docx (das an dem wir bis letzte Woche gearbeitet haben) einfügen.

===== Lesedatenmodelle (Materializes Views)

//Todo Lese-Datenmodelle aufzählen und kurz (1-2 Sätze) erklären wofür die fachlich(!) benötigt werden. (Hier NICHT schreiben "Wird für REST-Endpunkt /XYZ benötigt."!!!)

==== Technologie Stack

[Attributes]
|===
|Technologie |Zweck |Vorteil |Version

|Java
|Programmiersprache mit Typsicherheit.
|Bekannt für ihre Portabilität, Leistung und Robustheit. 
|`17`

|Spring Boot 
|Bietet Infrastrukturunterstützung für Java-Anwendungen.
|Sorgt für nahtlose Integration und Unterstützung verschiedener Funktionen wie Dependency Injection, Sicherheit und Datenzugriff.
|`3.2`

|AxonIQ 
|Implementiert CQRS (Command Query Responsibility Segregation) und Event Sourcing.
|Ermöglicht eine skalierbare und wartbare Architektur.
|`4.9`

|Liquibase
|Versionskontrolle für Datenbankschemata.
|Sichert zuverlässige und wiederholbare Datenbankmigrationen.
|`4`

|MariaDB
|Relationales Datenbankmanagementsystem.
|Speichert und verwaltet effizient die Query Daten (Views) sowie andere System notwendig Infos.
|`11.3`

|Drools 
|Business Rules Verwaltung System.
|Ermöglicht es, Business Rules in deklarativer Form zu schreiben, was die Mandantenfähigkeit der Anwendung unterstützt.
|`8.44`

|JUnit 
|Testframework.
|Ermöglicht das Schreiben und Ausführen wiederholbarer automatisierter Tests, wodurch die Codequalität und -zuverlässigkeit sichergestellt wird.
|`5`

|Gradle 
|Build-Automatisierungstool.
|Kompiliert, testet und stellt die Anwendung effizient bereit.
|`8`

|===

=== Fachverfahren

//TODO hier sollte ergänzt werden, was das die Komponente Fachverfahren macht. (ggf. aus dem Lastenheft...?)

Die zentrale Komponente des Systems ist das Fachverfahren, welches als grafische Benutzeroberfläche (GUI) den Behördenmitarbeitenden ermöglicht, Vorgänge im Bereich Fischerei effizient zu bearbeiten. Diese Lösung wird als Single-Page-Webanwendung implementiert. 

==== Entwicklungsansatz

Für die Entwicklung der Webanwendung setzen wir auf die Programmiersprache TypeScript und das Angular-Framework. Die Wahl von Angular erlaubt uns, eine modulare, wartbare und performante Anwendung zu erstellen, die in der Lage ist, komplexe Benutzerinteraktionen und Datenverarbeitungen zu implementieren.

==== Kommunikationsstrategie

Die Webanwendung kommuniziert über eine REST-API mit dem zentralen Register. Diese API ermöglicht es, Daten sicher und effizient zwischen der Benutzeroberfläche und dem Backend auszutauschen. Durch die Verwendung von REST-Standards stellen wir sicher, dass die Schnittstelle leicht verständlich, erweiterbar und kompatibel mit anderen Systemen ist.

==== Authentifizierung und Autorisierung

Die Authentifizierung der Behördenmitarbeitenden erfolgt über ein Login mit Benutzername und Passwort. Dieses Authentifizierungsverfahren wird durch OAuth 2.0 abgesichert, welches als Industriestandard für sichere Autorisierung weit verbreitet ist. Die Integration mit dem vorhandenen Identity and Access Management (IAM) System stellt sicher, dass nur berechtigte Nutzer Zugang zu den Funktionen der Webanwendung erhalten. OAuth 2.0 bietet hierbei eine robuste und flexible Lösung, um die Sicherheit und Benutzerfreundlichkeit zu gewährleisten.

==== Technologie Stack

[Attributes]
|===
|Technologie |Zweck |Vorteil |Version

|TypeScript
|Programmiersprache mit Typsicherheit. `Typisierte Superset von JavaScript.`
|Bietet statische Typisierung, die Fehler frühzeitig erkennt und die Codequalität verbessert.
|`5`

|Angular 
|Framework zum Erstellen dynamischer und moderner Webanwendungen.
|Bietet eine robuste Plattform für die WebClient-Side Entwicklung.
|`17`

|RxJS 
|Bibliothek für reaktive Programmierung. 
|Ermöglicht asynchrone Datenströme.
|`7.8`

|Cypress
|End-to-End-Testwerkzeug.
|Sichert, dass die Anwendung aus Sicht des Benutzers wie erwartet funktioniert. 
|`13`

|Tailwind CSS 
|Utility-first CSS-Framework.
|Bietet Flexibilität und Effizienz beim Styling der Anwendung.
|`3.4`

|Storybook 
|Tool zur Entwicklung von UI-Komponenten.
|Ermöglicht die Entwicklung von UI-Komponenten isoliert und erleichtert das UI-Testing.
|`8`

|===



=== Kontroll-App
//Todo Funktion beschreiben -> Was soll die App im Gesamtkontext machen?

Die Kontroll-App wird mit Googles Cross-Platform Software Development Kit (SDK) Flutter entwickelt. Die Nutzung des Flutter SDKs ermöglicht die Entwicklung von mobilen Anwendungen für mehrere Betriebssysteme, unter anderem Android oder iOS, auf Basis von einer einheitlichen Codebasis. Hardwarefunktionalitäten, wie Kamera-Ansteuerung, können über Plugins angebunden werden. 

Auf Basis des Codes werden Apps für Android und iOS erstellt, die im Apple App Store und im Google Play Store öffentlich einsehbar vertrieben werden. 

Zur Anbindung der Authentifizierung wird ein Plugin eingebunden, welches die OAuth2 Schnittstelle des Keycloaks nutzt. Hierdurch wird in der Kontroll-App der Authentifizierungsprozess geöffnet. Eine erfolgreiche Authentifizierung startet die Sitzung und die Anwendung kann genutzt werden. 

Zur Kontrolle einer gültigen Fischereilizenz werden Plugins zum Auslesen von NFC-Chips und zum Auslesen von QR-Codes eingebunden. Beim Scan des NFC-Chips beziehungsweise des QR-Codes werden die ausgelesenen Daten genutzt, um eine Abfrage gegen die API-Anwendung gestartet. Das Ergebnis der Abfrage beinhaltet die Information, ob die Lizenz gültig ist. 

Alternativ wird für die manuelle Prüfung ein Formular bereitgestellt, in welchem der Kontrolleur durch die Eingabe von Personendaten die API-Anwendungsabfrage startet. 

==== Technologie Stack

[Attributes]
|===
|Technologie |Zweck |Vorteil |Version

|Flutter SDK
|Cross-Plattform SDK 
|Entwicklung von Multi-Plattform Anwendungen für Android und iOS auf einer gemeinsamen Codebasis
|`3.22.0`

|Riverpod 
|Reaktiver Cache und Data-Binding Framework 
|Strukturiertes cachen von Daten und reaktives View-Binding 
|`2.4.5`

|GetIt 
|Dependency-Injection 
|Dependency-Inversion-Principle, Testbarkeit
|`7.7.0`

|nfc_manager 
|Ermöglicht die Verwaltung und Konfiguration von Nahfeldkommunikationsfunktionen auf einem Gerät, einschließlich Aktivierung, Deaktivierung und Anpassung der Einstellungen.
|Intuitive Schnittstelle zur einfachen Verwaltung und Konfiguration von NFC-Funktionen.
|`3.5.0`

|mobile_scanner 
|Zur Verwendung eines QR Code Scanners 
|Implementierung durch direktes anwenden als Flutter Widget 
|`5.1.1`

|goRouter 
|Zur Strukutrierung des Routings
|Vereinfachung des Routing durch Generierung
|`12.0.1`

|flutter_web_auth_2 
|Ein Flutter-Plugin zur Authentifizierung eines Benutzers mit einem Webdienst.
|automatische Weiterleitung
|`3.0.0`

|===


=== Identity and Access Management (IAM)

Für Authentisierung und Autorisierung wird  Keycloak eingesetzt. Keycloak stellt für das Fachverfahren und die Kontroll-App Bearer-Token aus. Das Register kann den Keycloak für Authentisierungs- und Autorisierungsabfragen aufrufen.

Keycloak bietet Basisfunktionen für die Benutzerverwaltung an. Weitere Software für die Verwaltung von Benutzern wird nicht geliefert.

Der Keycloak dient gleichzeitig als Audit-Log für die Benutzerverwaltung. Dafür wird die Eingebaute Audit-Funktionalitäten genutzt.



// Register und So
// Spring Boot 
// Anwendung haben wir uns für Angular entschieden.
// siehe S. 8 des Architekturdokumentes - 3. Domäne, 5. Architekturstil
