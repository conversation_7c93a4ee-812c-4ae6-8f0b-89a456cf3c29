<svg width="321" height="124" viewBox="0 0 321 124" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_bddddi_33_4724)">
<path d="M79.828 26.028C79.828 29.868 79.188 33.084 77.908 35.676C76.628 38.268 74.74 40.22 72.244 41.532C69.748 42.844 66.676 43.5 63.028 43.5H55.012V9.228H64.036C67.428 9.228 70.292 9.868 72.628 11.148C74.996 12.428 76.788 14.316 78.004 16.812C79.22 19.308 79.828 22.38 79.828 26.028ZM76.9 26.124C76.9 22.892 76.404 20.22 75.412 18.108C74.452 15.964 72.98 14.364 70.996 13.308C69.012 12.22 66.516 11.676 63.508 11.676H57.796V41.052H62.884C67.588 41.052 71.092 39.82 73.396 37.356C75.732 34.86 76.9 31.116 76.9 26.124ZM94.5603 43.5H84.9123V41.772L88.3203 41.244V11.532L84.9123 10.956V9.228H94.5603V10.956L91.1043 11.532V41.244L94.5603 41.772V43.5ZM114.237 26.076H125.949V41.964C124.349 42.636 122.669 43.148 120.909 43.5C119.181 43.82 117.309 43.98 115.293 43.98C111.869 43.98 108.989 43.292 106.653 41.916C104.349 40.508 102.589 38.492 101.373 35.868C100.189 33.244 99.5965 30.092 99.5965 26.412C99.5965 22.924 100.253 19.868 101.565 17.244C102.877 14.588 104.765 12.508 107.229 11.004C109.693 9.468 112.653 8.7 116.109 8.7C117.869 8.7 119.533 8.876 121.101 9.228C122.669 9.548 124.157 10.06 125.565 10.764L124.509 13.212C123.165 12.54 121.773 12.044 120.333 11.724C118.925 11.372 117.469 11.196 115.965 11.196C113.181 11.196 110.781 11.836 108.765 13.116C106.749 14.396 105.197 16.172 104.109 18.444C103.021 20.716 102.477 23.372 102.477 26.412C102.477 29.708 102.973 32.476 103.965 34.716C104.989 36.956 106.477 38.652 108.429 39.804C110.413 40.956 112.861 41.532 115.773 41.532C116.829 41.532 117.789 41.484 118.653 41.388C119.549 41.26 120.365 41.1 121.101 40.908C121.869 40.716 122.573 40.508 123.213 40.284V28.572H114.237V26.076ZM141.342 43.5H131.694V41.772L135.102 41.244V11.532L131.694 10.956V9.228H141.342V10.956L137.886 11.532V41.244L141.342 41.772V43.5Z" fill="#C8D1F0"/>
</g>
<g filter="url(#filter1_bddddi_33_4724)">
<path d="M28.796 80.5H26.012V46.228H43.724V48.724H28.796V62.836H42.908V65.332H28.796V80.5ZM56.279 80.5H46.631V78.772L50.039 78.244V48.532L46.631 47.956V46.228H56.279V47.956L52.823 48.532V78.244L56.279 78.772V80.5ZM80.8993 71.62C80.8993 73.668 80.4193 75.396 79.4593 76.804C78.4993 78.18 77.1713 79.22 75.4753 79.924C73.8113 80.628 71.9073 80.98 69.7633 80.98C68.4513 80.98 67.2513 80.9 66.1633 80.74C65.1073 80.612 64.1473 80.452 63.2833 80.26C62.4513 80.036 61.6673 79.78 60.9313 79.492V76.708C62.1153 77.188 63.4593 77.62 64.9633 78.004C66.4673 78.356 68.0993 78.532 69.8593 78.532C71.5233 78.532 72.9633 78.276 74.1793 77.764C75.4273 77.252 76.3873 76.5 77.0593 75.508C77.7633 74.516 78.1153 73.268 78.1153 71.764C78.1153 70.388 77.8113 69.268 77.2033 68.404C76.6273 67.54 75.7313 66.788 74.5153 66.148C73.3313 65.476 71.7953 64.804 69.9073 64.132C68.5953 63.62 67.4113 63.092 66.3553 62.548C65.2993 62.004 64.3873 61.364 63.6193 60.628C62.8833 59.892 62.3073 59.028 61.8913 58.036C61.5073 57.044 61.3153 55.844 61.3153 54.436C61.3153 52.548 61.7633 50.964 62.6593 49.684C63.5553 48.404 64.7713 47.428 66.3073 46.756C67.8753 46.084 69.6513 45.748 71.6353 45.748C73.2353 45.748 74.7233 45.908 76.0993 46.228C77.5073 46.516 78.8513 46.964 80.1313 47.572L79.2193 49.972C77.9393 49.396 76.6593 48.964 75.3793 48.676C74.0993 48.388 72.8193 48.244 71.5393 48.244C70.0673 48.244 68.7713 48.484 67.6513 48.964C66.5633 49.412 65.6993 50.1 65.0593 51.028C64.4513 51.924 64.1473 53.044 64.1473 54.388C64.1473 55.86 64.4513 57.044 65.0593 57.94C65.6673 58.836 66.5313 59.588 67.6513 60.196C68.8033 60.772 70.1793 61.364 71.7793 61.972C73.6993 62.644 75.3313 63.38 76.6753 64.18C78.0513 64.98 79.0913 65.956 79.7953 67.108C80.5313 68.26 80.8993 69.764 80.8993 71.62ZM101.333 48.244C99.3806 48.244 97.6206 48.612 96.0526 49.348C94.5166 50.052 93.2046 51.076 92.1166 52.42C91.0606 53.732 90.2446 55.316 89.6686 57.172C89.1246 58.996 88.8526 61.028 88.8526 63.268C88.8526 66.34 89.3006 69.028 90.1966 71.332C91.0926 73.604 92.4366 75.364 94.2286 76.612C96.0206 77.86 98.2446 78.484 100.901 78.484C102.469 78.484 103.909 78.356 105.221 78.1C106.533 77.812 107.765 77.476 108.917 77.092V79.54C107.829 79.988 106.613 80.34 105.269 80.596C103.957 80.852 102.405 80.98 100.613 80.98C97.3806 80.98 94.6766 80.244 92.5006 78.772C90.3246 77.3 88.6766 75.236 87.5566 72.58C86.4686 69.924 85.9246 66.82 85.9246 63.268C85.9246 60.74 86.2606 58.404 86.9326 56.26C87.6366 54.116 88.6446 52.26 89.9566 50.692C91.2686 49.124 92.8846 47.908 94.8046 47.044C96.7246 46.18 98.9166 45.748 101.381 45.748C102.949 45.748 104.437 45.908 105.845 46.228C107.285 46.548 108.629 47.012 109.877 47.62L108.773 50.068C107.621 49.46 106.421 49.012 105.173 48.724C103.957 48.404 102.677 48.244 101.333 48.244ZM139.49 80.5H136.706V63.796H118.562V80.5H115.778V46.228H118.562V61.3H136.706V46.228H139.49V80.5Z" fill="#163BBF" fill-opacity="0.96"/>
</g>
<g filter="url(#filter2_bddddi_33_4724)">
<path d="M79.828 100.028C79.828 103.868 79.188 107.084 77.908 109.676C76.628 112.268 74.74 114.22 72.244 115.532C69.748 116.844 66.676 117.5 63.028 117.5H55.012V83.228H64.036C67.428 83.228 70.292 83.868 72.628 85.148C74.996 86.428 76.788 88.316 78.004 90.812C79.22 93.308 79.828 96.38 79.828 100.028ZM76.9 100.124C76.9 96.892 76.404 94.22 75.412 92.108C74.452 89.964 72.98 88.364 70.996 87.308C69.012 86.22 66.516 85.676 63.508 85.676H57.796V115.052H62.884C67.588 115.052 71.092 113.82 73.396 111.356C75.732 108.86 76.9 105.116 76.9 100.124ZM114.528 100.316C114.528 102.94 114.224 105.324 113.616 107.468C113.008 109.612 112.096 111.484 110.88 113.084C109.664 114.652 108.144 115.868 106.32 116.732C104.496 117.564 102.384 117.98 99.9843 117.98C97.5523 117.98 95.4243 117.548 93.6003 116.684C91.7763 115.82 90.2563 114.604 89.0403 113.036C87.8243 111.468 86.9123 109.612 86.3043 107.468C85.7283 105.292 85.4403 102.892 85.4403 100.268C85.4403 96.78 85.9843 93.724 87.0723 91.1C88.1923 88.476 89.8403 86.428 92.0163 84.956C94.1923 83.452 96.8963 82.7 100.128 82.7C103.232 82.7 105.856 83.42 108 84.86C110.144 86.268 111.76 88.284 112.848 90.908C113.968 93.532 114.528 96.668 114.528 100.316ZM88.3683 100.268C88.3683 103.34 88.7843 106.012 89.6163 108.284C90.4483 110.556 91.7283 112.332 93.4563 113.612C95.1843 114.892 97.3763 115.532 100.032 115.532C102.688 115.532 104.864 114.908 106.56 113.66C108.288 112.38 109.552 110.604 110.352 108.332C111.184 106.06 111.6 103.388 111.6 100.316C111.6 95.548 110.64 91.836 108.72 89.18C106.8 86.524 103.936 85.196 100.128 85.196C97.4723 85.196 95.2643 85.82 93.5043 87.068C91.7763 88.316 90.4803 90.076 89.6163 92.348C88.7843 94.62 88.3683 97.26 88.3683 100.268ZM143.985 117.5H140.673L128.577 99.74L124.593 103.82V117.5H121.809V83.228H124.593V101.036C125.201 100.3 125.825 99.564 126.465 98.828C127.137 98.06 127.793 97.292 128.433 96.524L140.241 83.228H143.649L130.593 97.82L143.985 117.5Z" fill="#C8D1F0"/>
</g>
<rect x="168.5" width="2" height="124" fill="#9FAFE6"/>
<g filter="url(#filter3_bddddi_33_4724)">
<path d="M223 47.5L253.5 17L298.5 62L253.5 107L223 76.5L215.5 84L208.5 77L223.5 62L208.5 47L215.5 40L223 47.5Z" fill="white" fill-opacity="0.24"/>
</g>
<g filter="url(#filter4_bddddi_33_4724)">
<path d="M245.5 25L282.5 62L245.5 99L216.5 70L202.5 84L194.5 76L208.5 62L194.5 48L202.5 40L216.5 54L245.5 25Z" fill="#163BBF" fill-opacity="0.96"/>
</g>
<path fill-rule="evenodd" clip-rule="evenodd" d="M222.914 63.4142L221.5 62L222.914 60.5858L224.328 59.1716L227.5 56L230.672 52.8284L233.5 50L238.086 45.4142L239.5 44L240.914 45.4142L256.086 60.5858L257.5 62L256.086 63.4142L240.914 78.5858L239.5 80L238.086 78.5858L233.5 74L230.672 71.1716L227.5 68L224.328 64.8284L222.914 63.4142ZM230.328 65.1716L233.5 68.3431L239.843 62L233.5 55.6569L230.328 58.8284L233.5 62L230.328 65.1716ZM236.328 71.1716L234.914 72.5858L239.5 77.1716L254.672 62L239.5 46.8284L234.914 51.4142L236.328 52.8284L242.672 59.1716L245.5 62L242.672 64.8284L236.328 71.1716Z" fill="white" fill-opacity="0.24"/>
<defs>
<filter id="filter0_bddddi_33_4724" x="31.012" y="-15.3" width="134.329" height="93.28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_33_4724" result="effect2_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="3"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_33_4724" result="effect3_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="14"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_33_4724" result="effect4_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="24"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_33_4724" result="effect5_dropShadow_33_4724"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_33_4724" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect6_innerShadow_33_4724"/>
</filter>
<filter id="filter1_bddddi_33_4724" x="2.01199" y="21.748" width="161.478" height="93.232" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_33_4724" result="effect2_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="3"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_33_4724" result="effect3_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="14"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_33_4724" result="effect4_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="24"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_33_4724" result="effect5_dropShadow_33_4724"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_33_4724" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect6_innerShadow_33_4724"/>
</filter>
<filter id="filter2_bddddi_33_4724" x="31.012" y="58.7" width="136.973" height="93.28" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_33_4724" result="effect2_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="3"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_33_4724" result="effect3_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="14"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_33_4724" result="effect4_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="24"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_33_4724" result="effect5_dropShadow_33_4724"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_33_4724" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect6_innerShadow_33_4724"/>
</filter>
<filter id="filter3_bddddi_33_4724" x="184.5" y="-7" width="138" height="148" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_33_4724" result="effect2_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="3"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_33_4724" result="effect3_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="14"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_33_4724" result="effect4_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="24"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_33_4724" result="effect5_dropShadow_33_4724"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_33_4724" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect6_innerShadow_33_4724"/>
</filter>
<filter id="filter4_bddddi_33_4724" x="170.5" y="1" width="136" height="132" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="1.5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
<feBlend mode="normal" in2="effect1_backgroundBlur_33_4724" result="effect2_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="3"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
<feBlend mode="normal" in2="effect2_dropShadow_33_4724" result="effect3_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="14"/>
<feGaussianBlur stdDeviation="4"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
<feBlend mode="normal" in2="effect3_dropShadow_33_4724" result="effect4_dropShadow_33_4724"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="24"/>
<feGaussianBlur stdDeviation="5"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
<feBlend mode="normal" in2="effect4_dropShadow_33_4724" result="effect5_dropShadow_33_4724"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_33_4724" result="shape"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="2"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
<feBlend mode="normal" in2="shape" result="effect6_innerShadow_33_4724"/>
</filter>
</defs>
</svg>
