workspace {

    model {
        // People
        Buerger = person "Bürger" "Person die einen Fischereischein beantragt."
        Behoerdenmitarbeiter = person "Behördenmitarbeiter" "Nutzer des Fischereifachverfahrens für die Verwaltung von Fischereischeinen." "User"
        Kontrolleur = person "Kontrolleur" "Fischereiaufsicht jeglicher Art." "User"
        Pruefer = person "Prüfer" "Ein Nutzer des Fachverfahrens, welcher Prüfungszeugnisse digital registriert." "Staff"
        Benutzeradministrator = person "Benutzer-administrator" "Administriert die Nutzer des Fischereifachverfahrens und der Kontrollanwendung im IAM" "Staff"

        webbrowser = softwareSystem "Web Browser" "" "Web Browser"

        // Softwaresystems: Under Development
        fischSystem = softwareSystem "Gesamtsystem Fischereifachverfahren" {
            backend = container "Register" "Bietet Fischereifachverfahren-Funktionen über JSON/HTTPS-API an." "Java und Spring Boot 3" "Backend" {
                // RestController
                citizenOverviewController = component "Citizen Overview Controller" "tbd." "Spring Web RestController"
                digitizeFishingLicenseController = component "Digitize Fishing License Controller" "tbd." "Spring Web RestController"
                fishingTaxController = component "Fishing Tax Controller" "tbd." "Spring Web RestController"
                licenseFeeController = component "License Fee Controller" "tbd." "Spring Web RestController"
                tenantConfigurationController = component "Tenant Configuration Controller" "tbd." "Spring Web RestController"
                cardOrderController = component "Order fishing license cards" "Spring Web RestController"
                // Spring Beans
                securityComponent = component "Security Component" "Bietet Funktionen rund um Benutzerberechtigungen an." "Spring Bean and Oauth2"
                // Spring Services
                droolsService = component "Drools Service" "tbd." "Spring Service und Drools"
                cardOrderService = component "Card Order Service" "Manages card orders." "Spring Service"

                actuatorController = component "Spring Actuator" "Provides Health Check Resources, and System Informations" "Spring Actuator"
            }
            // @TODO es wäre schön wenn wir genauer beschreiben welche Daten wir in der Datenbank speichern.
            database = container "Datenbank" "Speichert die Daten die im Fischereifachverfahren bearbeitet werden." "MariaDB" "Database, Existing System"
            fachanwendung = container "Web Anwendung" "Bietet Behördenmitarbeitern und Prüferen sämtliche Fischereifachverfahren-Funktionen über ihren Webbrowser an." "TypeScript und Angular"
            // @TOOD es wäre schön wenn wir genauer beschreiben welche Funktionen die Kontroll-App anbietet.
            kontrollapp = container "Kontroll-App" "Bietet Kontrolleuren eine Teil der Fischereifachverfahren-Funktionen über ihr Mobilgerät an." "Dart und Flutter" "Mobile App" {
                auth = component "Authentifizierung" "Händelt den Authentifizierungsflow und stellt eine App-Sitzung her"
                barcodeScanner = component "QR-Code Scanner" "Zur Kontrolle durch das Scannen von QR-Codes"
                document = component "Rechtliche Dokumentation" "Beinhaltet rechtliche Dokumentation, wie AGBs, Datenschutzerklärung und Nutzungsbedingungen"
                manualControl = component "Manuelle Kontrolle" "Zur Kontrolle durch die Eingabe von manuellen Eingaben"
                nfcScanner = component "NFC Scanner" "Zur Kontrolle durch das Scannen von NFC-Chips"
                support = component "Support" "Nutzer können Probleme mit der Handhabung der Mobilen Apps melden"
            }
        }

        // Softwaresystems: Existing System
        pruefungsSystem = softwareSystem "Prüfungssoftware" "Digitale Prüfungsablage" "Existing System"
        keycloakSystem = softwareSystem "Keycloak" "Identitätsmanagement (Idm)" "Existing System"
        emailSystem = softwareSystem "E-Mail Provider" "Beliebiger Provider" "Existing System"
        kartendruckdienstleisterSystem = softwareSystem "Kartendruckdienstleister" "Dienstleister für die Herstellung von physischen Karten für den Fischereischein" {
            tags "Existing System"
            orderServiceForCardPrinting = container "Order Service for Card Printing"
        }
        fischereiOnlineDiensteSystem = softwareSystem "Fischerei Online Dienst(e)" "Öffentliches System zur digitalen Beantragung von Fischereischeinen durch den Bürger." "Existing System"

        // relationships between people and Softwaresystems
        Pruefer -> pruefungsSystem "registriert Prüfungszeugnisse" "manuell"
        Benutzeradministrator -> keycloakSystem "verwaltet Benutzer" "Webbrowser"
        Buerger -> kartendruckdienstleisterSystem "erhält per Post Fischereilizenz Karte" "Deutsche Post"

        // relationships between Softwaresystems
        pruefungsSystem -> fischSystem "registriert Prüfungsergebnisse" "Schnittstelle 1: Prüfungssoftware - Register"
        fischSystem -> keycloakSystem "authentifiziert und authorisiert Benutzer" "Schnittstelle 6: Gesamtsystem Fischereifachverfahren - Keycloak"
        // das ergibt für mich wenig Sinn, das ein Email Provider Nachweise digital ablegt?
        fischSystem -> emailSystem "legt Nachweise als digitale Dokumente ab" "Schnitstelle 8: Register - E-Mail"
        fischereiOnlineDiensteSystem -> fischSystem "stellt Fischereianträge, bezahlt Abgaben" "Schnittstelle 2: Onlinedienst - Register"

        // relationships to/from CONTAINER(s)
        Kontrolleur -> kontrollapp "prüft Fischereischeine und Fischereiabgaben" "Schnittstelle 4: Kontrollapp - Register"
        Behoerdenmitarbeiter -> fachanwendung "bearbeitet Vorgänge"
        fachanwendung -> backend "ruft die API auf" "JSON/HTTPS"
        fachanwendung -> keycloakSystem "Login/Logout von Behördenmitarbeitern" "HTTPS"
        kontrollapp -> backend "ruft die API auf" "JSON/HTTPS"
        kontrollapp -> keycloakSystem "Login/Logout von Kontrolleuren" "HTTPS"
        backend -> database "Reads from and writes to" "SQL/TCP"
        backend -> keycloakSystem "prüft die autorisierung von Requests" "HTTPS"

        fischSystem -> orderServiceForCardPrinting "sendet Karten Bestellung(en) an" "JSON/HTTPS"
        orderServiceForCardPrinting -> cardOrderController "sendet Bestellstatusupdate(s)" "JSON/HTTPS"
        // Needed for dynamic diagrams
        kartendruckdienstleisterSystem -> kartendruckdienstleisterSystem

        // relationships to/from COMPONENT(s)
        fachanwendung -> citizenOverviewController "tbd." "HTTPS"
        fachanwendung -> digitizeFishingLicenseController "tbd." "HTTPS"
        fachanwendung -> fishingTaxController "tbd." "HTTPS"
        fachanwendung -> licenseFeeController "tbd." "HTTPS"
        fachanwendung -> tenantConfigurationController "tbd." "HTTPS"

        fachanwendung -> cardOrderController "gibt eine Karten Bestellung auf" "HTTPS"

        kontrollapp -> citizenOverviewController "tbd." "HTTPS"
        kontrollapp -> digitizeFishingLicenseController "tbd." "HTTPS"
        kontrollapp -> fishingTaxController "tbd." "HTTPS"
        kontrollapp -> licenseFeeController "tbd." "HTTPS"
        kontrollapp -> tenantConfigurationController "tbd." "HTTPS"

        // relationships from COMPONENT(s) to COMPONENT(s)
        citizenOverviewController -> securityComponent "Uses"
        digitizeFishingLicenseController -> securityComponent "Uses"
        fishingTaxController -> securityComponent "Uses"
        licenseFeeController -> securityComponent "Uses"
        tenantConfigurationController -> securityComponent "Uses"
        digitizeFishingLicenseController -> droolsService "Uses"
        cardOrderController -> cardOrderService "Uses"

        // relationships from COMPONENT(s) to CONTAINER(s)
        cardOrderService -> orderServiceForCardPrinting "sendet die Bestellung mit der Bestellnumber"
        orderServiceForCardPrinting -> cardOrderController "sendet Status Updates zur Bestellung"

        // DEPLOYMENT ENVIRONMENTS
        deploymentEnvironment "LocalDevelopment" {
            deploymentNode "Developer Laptop" "" "Microsoft Windows or Apple macOS" {
                deploymentNode "Kubernetes" "Docker Desktop - Kubernetes" "Kubernetes" {
                    developerPodRegisterService = deploymentNode "pod/register-service" "" "pod/oci" "Kubernetes - pod" 1 {
                        developerPodBackendInstance = containerInstance backend
                    }
                    developerPodMariaDb = deploymentNode "pod/mariadb" "" "pod/oci" "Kubernetes - pod" 1 {
                        developerPodMariaDbInstance = containerInstance database {
                            tags "Software System"
                        }
                    }
                    developerPodWebApp = deploymentNode "pod/web-app" "" "pod/oci" "Kubernetes - pod" 1 {
                        developerPodFachanwendungInstance = containerInstance fachanwendung
                    }
                    developerSrvRegisterService = infrastructureNode "service/register-service" "" "service" "Kubernetes - service" {
                        -> developerPodBackendInstance
                    }
                    developerSrvMariaDb = infrastructureNode "service/mariadb" "" "service" "Kubernetes - service" {
                        -> developerPodMariaDbInstance
                    }
                    developerSrvWebApp = infrastructureNode "service/web-app" "" "service" "Kubernetes - service" {
                        -> developerPodFachanwendungInstance
                    }
                }
                deploymentNode "Web Browser" "" "Firefox, Chrome, Edge, Safari, ..." {
                    softwareSystemInstance webbrowser {
                        -> developerSrvWebApp "" "HTTP/HTTPS"
                    }
                }

                deploymentNode "Simulator Android / iOS" "" "" {
                    developerKontrollAppInstance = containerInstance kontrollapp
                }

                developerPodWebApp -> developerSrvRegisterService "ruft die API auf" "JSON/HTTPS"
                developerPodRegisterService -> developerSrvMariaDb "Reads from and writes to" "SQL/TCP"
                developerKontrollAppInstance -> developerSrvRegisterService "ruft die API auf" "JSON/HTTPS"
            }
            deploymentNode "adesso Cloud" "" "" {
                developerKeycloakInstance = softwareSystemInstance keycloakSystem
            }
        }

        deploymentEnvironment "Dev" {
            deploymentNode "Developer's mobile device" "" "Android / iOS" {
                devKontrollAppInstance = containerInstance kontrollapp
            }
            deploymentNode "dataport - Dev" {
                devVirtualMachines = deploymentNode "Virtual Machines" "" "mariaDb" "Virtual Machine" 1 {
                    devGaleriaMariaDbClusterInstance = containerInstance database
                    devKeycloakInstance = softwareSystemInstance keycloakSystem
                }
                deploymentNode "Kubernetes" "Dataport - Kubernetes Cluster - Dev" "Kubernetes/Rancher" {
                    devPodRegisterService = deploymentNode "pod/register-service" "" "pod/oci" "Kubernetes - pod" 1 {
                        devPodBackendInstance = containerInstance backend
                    }
                    devPodWebApp = deploymentNode "pod/web-app" "" "pod/oci" "Kubernetes - pod" 1 {
                        devPodFachanwendungInstance = containerInstance fachanwendung
                    }
                    devSrvRegisterService = infrastructureNode "service/register-service" "" "service" "Kubernetes - service" {
                        -> devPodBackendInstance
                    }
                    devSrvWebApp = infrastructureNode "service/web-app" "" "service" "Kubernetes - service" {
                        -> devPodFachanwendungInstance
                    }
                }
                devPodWebApp -> devSrvRegisterService "ruft die API auf" "JSON/HTTPS"
                devKontrollAppInstance -> devSrvRegisterService "ruft die API auf" "JSON/HTTPS"
            }
            deploymentNode "Developer's computer" "" "Microsoft Windows or Apple macOS" {
                deploymentNode "Web Browser" "" "Firefox, Chrome, Edge, Safari, ..." {
                    softwareSystemInstance webbrowser {
                        -> devSrvWebApp "" "HTTP/HTTPS"
                    }
                }
            }
        }

        deploymentEnvironment "Test" {
            deploymentNode "Developer's mobile device" "" "Android / iOS" {
                testKontrollAppInstance = containerInstance kontrollapp
            }
            deploymentNode "dataport - Test" {
                testVirtualMachines = deploymentNode "Virtual Machines" "" "mariaDb" "Virtual Machine" 1 {
                    testGaleriaMariaDbClusterInstance = containerInstance database
                    testKeycloakInstance = softwareSystemInstance keycloakSystem
                }
                deploymentNode "Kubernetes" "Dataport - Kubernetes Cluster - Test" "Kubernetes/Rancher" {
                    testPodRegisterService = deploymentNode "pod/register-service" "" "pod/oci" "Kubernetes - pod" 1 {
                        testPodBackendInstance = containerInstance backend
                    }
                    testPodWebApp = deploymentNode "pod/web-app" "" "pod/oci" "Kubernetes - pod" 1 {
                        testPodFachanwendungInstance = containerInstance fachanwendung
                    }
                    testSrvRegisterService = infrastructureNode "service/register-service" "" "service" "Kubernetes - service" {
                        -> testPodBackendInstance
                    }
                    testSrvWebApp = infrastructureNode "service/web-app" "" "service" "Kubernetes - service" {
                        -> testPodFachanwendungInstance
                    }
                }
                testPodWebApp -> testSrvRegisterService "ruft die API auf" "JSON/HTTPS"
                testKontrollAppInstance -> testSrvRegisterService "ruft die API auf" "JSON/HTTPS"
            }
            deploymentNode "Developer's computer" "" "Microsoft Windows or Apple macOS" {
                deploymentNode "Web Browser" "" "Firefox, Chrome, Edge, Safari, ..." {
                    softwareSystemInstance webbrowser {
                        -> testSrvWebApp "" "HTTP/HTTPS"
                    }
                }
            }
        }

        deploymentEnvironment "Prod" {
            deploymentNode "Kontrolleur mobile device" "" "Android / iOS" {
                prodKontrollAppInstance = containerInstance kontrollapp
            }
            deploymentNode "dataport - Prod" {
                prodVirtualMachines = deploymentNode "Virtual Machines" "" "mariaDb" "Virtual Machine" 1 {
                    prodGaleriaMariaDbClusterInstance = containerInstance database
                    prodKeycloakInstance = softwareSystemInstance keycloakSystem
                }
                deploymentNode "Kubernetes" "Dataport - Kubernetes Cluster - Prod" "Kubernetes/Rancher" {
                    prodPodRegisterService = deploymentNode "pod/register-service" "" "pod/oci" "Kubernetes - pod" 1 {
                        prodPodBackendInstance = containerInstance backend
                    }
                    prodPodWebApp = deploymentNode "pod/web-app" "" "pod/oci" "Kubernetes - pod" 1 {
                        prodPodFachanwendungInstance = containerInstance fachanwendung
                    }
                    prodSrvRegisterService = infrastructureNode "service/register-service" "" "service" "Kubernetes - service" {
                        -> prodPodBackendInstance
                    }
                    prodSrvWebApp = infrastructureNode "service/web-app" "" "service" "Kubernetes - service" {
                        -> prodPodFachanwendungInstance
                    }
                }
                prodPodWebApp -> prodSrvRegisterService "ruft die API auf" "JSON/HTTPS"
                prodKontrollAppInstance -> prodSrvRegisterService "ruft die API auf" "JSON/HTTPS"
            }
            deploymentNode "Behördenmitarbeiter Computer" "" "Microsoft Windows or Apple macOS" {
                deploymentNode "Web Browser" "" "Firefox, Chrome, Edge, Safari, ..." {
                    softwareSystemInstance webbrowser {
                        -> prodSrvWebApp "" "HTTP/HTTPS"
                    }
                }
            }
        }

    }

    views {
        systemlandscape "SystemLandscape" {
            include *
            exclude "kartendruckdienstleisterSystem -> kartendruckdienstleisterSystem"
            exclude "webbrowser"

            // autoLayout
        }

        systemContext fischSystem "SystemContext" {
            include *
            exclude "kartendruckdienstleisterSystem -> kartendruckdienstleisterSystem"

            autolayout
            description "Das Systemkontextdiagramm für das Gesamtsystem Fischereifachverfahren."
        }

        container fischSystem "Containers" {
            include *
            exclude "kartendruckdienstleisterSystem -> kartendruckdienstleisterSystem"

            autoLayout
            description "Das Containerdiagramm für das Gesamtsystem Fischereifachverfahren."
        }

        component backend "Components" {
            include *
            exclude "kartendruckdienstleisterSystem -> kartendruckdienstleisterSystem"

            autoLayout
            description "Das Komponentendiagramm für die API-Anwendung."
        }

        component kontrollapp "Kontrollapp" {
            include *
            
            autoLayout
            description "Das Komponentendiagramm für die Mobile-Apps"
        }

        // SNIPPET: Insert Code Structure as UML Diagram which we can create with any UML tool.
        // 
        // image mainframeBankingSystemFacade "MainframeBankingSystemFacade" {
        //     image https://raw.githubusercontent.com/structurizr/examples/main/dsl/big-bank-plc/internet-banking-system/mainframe-banking-system-facade.png
        //     title "[Code] Mainframe Banking System Facade"
        // }

        deployment fischSystem "LocalDevelopment" "DevelopmentDeployment" {
            include *
            exclude "fachanwendung -> backend"
            exclude "backend -> database"
            exclude "kontrollapp -> backend"
            autoLayout
            description "Ein beispielhaftes Deploymentszenario für die lokale Entwicklung des Gesamtsystem Fischereifachverfahren"
        }

        deployment fischSystem "Dev" "DevDeployment" {
            include *
            exclude "fachanwendung -> backend"
            exclude "kontrollapp -> backend"
            autoLayout
            description "Ein beispielhaftes Deploymentszenario für die Dev Umgebung bei Dataport des Gesamtsystem Fischereifachverfahren"
        }

        deployment fischSystem "Test" "TestDeployment" {
            include *
            exclude "fachanwendung -> backend"
            exclude "kontrollapp -> backend"
            autoLayout
            description "Ein beispielhaftes Deploymentszenario für die Test Umgebung bei Dataport des Gesamtsystem Fischereifachverfahren"
        }

        deployment fischSystem "Prod" "ProdDeployment" {
            include *
            exclude "fachanwendung -> backend"
            exclude "kontrollapp -> backend"
            autoLayout
            description "Ein beispielhaftes Deploymentszenario für die Prod Umgebung bei Dataport des Gesamtsystem Fischereifachverfahren"
        }

        dynamic backend "cardPrintingProcess" {
            title "Summarises how the card printing/ordering in feature works."

            // Wie wird eine Bestellung angelegt / ausgelöst?
            // Frontend -> klickt einer auf Fischereischein/Karte bestellen -> Backend -> erstellt eine Order im System -> 
            fachanwendung ->  cardOrderController
            cardOrderController -> cardOrderService "erstellt eine Bestellnumber zur späteren Statusupdate übermittlung"

            cardOrderService -> orderServiceForCardPrinting "sendet die Bestellung mit der Bestellnumber"
            orderServiceForCardPrinting -> cardOrderController "sendet Status Updates zur Bestellung"

            autoLayout
        }

        theme default

        styles {
            element "Person" {
                color #ffffff
                fontSize 22
                shape Person
            }
            element "User" {
                background #08427b
            }
            element "Staff" {
                background #999999
            }
            element "Software System" {
                background #1168bd
                color #ffffff
            }
            element "Existing System" {
                background #999999
                color #ffffff
            }
            element "Container" {
                background #438dd5
                color #ffffff
            }
            element "Web Browser" {
                shape WebBrowser
            }
            element "Mobile App" {
                shape MobileDeviceLandscape
            }
            element "Database" {
                shape Cylinder
            }
            element "Component" {
                background #85bbf0
                color #000000
            }
            element "Failover" {
                opacity 25
            }

        }
    }

}
