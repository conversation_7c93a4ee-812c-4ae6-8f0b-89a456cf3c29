#!/usr/bin/env bash

# Function to display help message
show_help() {
    echo "Usage: $0 [options...]"
    echo "Options:"
    echo "  -h, --help            Show this help message and exit"
    echo "  -f, --file FILE       Specify the input OpenAPI file"
    echo "  -v, --verbose         Enable verbose mode"
}

# Default values
verbose=0
input_file=""

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--file)
            if [[ -n $2 && $2 != -* ]]; then
                input_file="$2"
                shift
            else
                echo "Error: --file requires a non-empty option argument."
                exit 1
            fi
            ;;
        -v|--verbose)
            verbose=1
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
    shift
done

# Verbose mode
if [[ $verbose -eq 1 ]]; then
    echo "Verbose mode enabled"
    echo "Input file: $input_file"
fi

# Main script logic
if [[ -n $input_file ]]; then
    # "postman": "yarn run postman:generate && yarn run jq:remove:event && yarn run jq:add:auth && yarn run jq:add:event && yarn run jq:cleanup:collection.json && yarn run jq:cleanup:collection.clean.json && yarn run jq:cleanup:collection.auth.json",
    echo "Processing input file: $input_file"

    # Extract filename without extension
    filename=$(basename "$input_file")
    filename_without_extension="${filename%.*}"
    
    # Extract directory path
    output_path=$(dirname "$input_file")

    postman_collection_filename="$output_path/$filename_without_extension.json"

    yarn openapi2postmanv2 -s "$input_file" -o "$postman_collection_filename" -p -c ./cli-options-config.json

    # "jq:remove:event": "jq 'del(.event)' ./tsp-output/@typespec/postman/collection.json > tsp-output/@typespec/postman/collection.clean.json",
    jq 'del(.event)' "$postman_collection_filename" > "$output_path/$filename_without_extension.clean.json"

    # "jq:add:auth": "jq -s add auth.json tsp-output/@typespec/postman/collection.clean.json > tsp-output/@typespec/postman/collection.auth.json",
    jq -s add src/generate-postman/auth.json "$output_path/$filename_without_extension.clean.json" > "$output_path/$filename_without_extension.auth.json"

    # "jq:add:event": "jq -s add event.json tsp-output/@typespec/postman/collection.auth.json > tsp-output/@typespec/postman/collection.event.json",
    jq -s add src/generate-postman/event.json "$output_path/$filename_without_extension.auth.json" > "$output_path/$filename_without_extension.event.json"

    # "jq:cleanup:collection.json": "rimraf tsp-output/@typespec/postman/collection.json",
    yarn rimraf "$output_path/$filename_without_extension.json"

    # "jq:cleanup:collection.clean.json": "rimraf tsp-output/@typespec/postman/collection.clean.json",
    yarn rimraf "$output_path/$filename_without_extension.clean.json"

    # "jq:cleanup:collection.auth.json": "rimraf tsp-output/@typespec/postman/collection.auth.json",
    yarn rimraf "$output_path/$filename_without_extension.auth.json"

    # Rename last patched file to a good collection name
    mv "$output_path/$filename_without_extension.event.json" "$output_path/postman-collection.$filename_without_extension.json"

else
    echo "No input file specified."
fi
