#!/usr/bin/env bash

# Function to display help message
show_help() {
    echo "Usage: $0 [options...]"
    echo "Options:"
    echo "  -h, --help            Show this help message and exit"
    echo "  -f, --file FILE       Specify the input OpenAPI file"
    echo "  -v, --verbose         Enable verbose mode"
}

# Default values
verbose=0
input_file=""

# Parse command line arguments
while [[ "$#" -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -f|--file)
            if [[ -n $2 && $2 != -* ]]; then
                input_file="$2"
                shift
            else
                echo "Error: --file requires a non-empty option argument."
                exit 1
            fi
            ;;
        -v|--verbose)
            verbose=1
            ;;
        *)
            echo "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
    shift
done

# Verbose mode
if [[ $verbose -eq 1 ]]; then
    echo "Verbose mode enabled"
    echo "Input file: $input_file"
fi

# Main script logic
if [[ -n $input_file ]]; then
    echo "Processing input file: $input_file"
    
    # Extract filename without extension
    filename=$(basename "$input_file")
    filename_without_extension="${filename%.*}"
    
    # Extract directory path
    output_path=$(dirname "$input_file")

    output_filename="$output_path/$filename_without_extension.md"

    # Clean Build files
    rm -f "$output_filename"

    widdershins \
        --environment ./generate-docs.config.yaml \
        "$input_file" \
        --outfile "./$output_filename"

else
    echo "No input file specified."
fi
