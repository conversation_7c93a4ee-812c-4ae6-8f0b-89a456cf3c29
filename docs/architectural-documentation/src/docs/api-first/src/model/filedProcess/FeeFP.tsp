import "../enum/FederalStateAbbreviation.tsp";
import "./PaymentInfoFP.tsp";

@doc("Fee connected to costly processes in the government office, i.e. ordering of a new check-card.")
model FeeFP {
    @doc("In which / For which federal state the fee was paid.")
    federalState: FederalStateAbbreviation;

    @doc("When the fee was payed.")
    validFrom: string;

    @doc("The indented end date for the fee.")
    validTo?: string;

    @doc("Information on how the payment was conducted.")
    paymentInfo: PaymentInfoFP;
}
