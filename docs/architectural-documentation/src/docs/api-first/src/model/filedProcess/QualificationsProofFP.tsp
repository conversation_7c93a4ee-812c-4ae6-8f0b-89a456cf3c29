import "../enum/QualificationsProofType.tsp";

@doc("A proof of qualification that is required for default fishing licenses.")
model QualificationsProofFP {
    @doc("Either CERTIFICATE or OTHER, depending of what is used as the proof for qualification.")
    type: QualificationsProofType;

    @doc("Which organisation issued the certificate")
    issuedBy: string;

    @doc("When the Proof was passed, format DD.MM.YYYY")
    passedOn: string;

    @doc("""
    Federal state where the certificate was passed.

    This may be left empty, if the proof did not originate in a german federal state.
    """)
    federalState?: FederalStateAbbreviation;

    @doc("Id when other identification form is used")
    otherFormOfProofId?: string;

    @doc("Id of the certificate")
    fishingCertificateId?: string;
}
