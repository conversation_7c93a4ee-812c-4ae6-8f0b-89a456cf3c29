import "./enum/FederalStateAbbreviation.tsp";
import "./PaymentInfo.tsp";

model Tax {
    @doc("In which / For which the fishing tax was paid.")
    federalState: FederalStateAbbreviation;

    @doc("Start date from which the tax should be valid.")
    validFrom: string;

    @doc("End date, to which the tax should be valid. This might be null in case of a lifelong tax, as may be the case in bavaria.")
    validTo?: string;

    @doc("Information on how the payment was conducted.")
    paymentInfo: PaymentInfo;
}
