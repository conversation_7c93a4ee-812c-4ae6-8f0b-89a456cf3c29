@doc("Contains information about consent or other neccessary to be confirmed information connected to a request to the API.")
model ConsentInfo {
    @doc("Indicates if the submission was made by a third party.")
    submittedByThirdParty: boolean;

    @doc("Indicates if GDPR terms have been accepted.")
    gdprAccepted: boolean;

    @doc("Indicates if self-disclosure terms have been accepted.")
    selfDisclosureAccepted: boolean;
}
