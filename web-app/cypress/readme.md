# Automated Tests with Cypress

## Introduction
This guide will walk you through setting up and running automated end-to-end tests using Cypress.

## Prerequisites
- Node.js and npm installed
- Basic knowledge of JavaScript and TypeScript

## Setup
1. **Clone the Repository**: Clone `***************************:8fd81c15/digifischdok.git` to your local machine.
2. **Login into** `<PERSON><PERSON><PERSON>` and register the artifact pool** `ischereiregister-npm`
```bash
npm login --registry=https://artifactory.adesso-group.com/artifactory/api/npm/fischereiregister-npm/ --auth-type=web
```
3. **Install Dependencies**: Navigate to `web-app`and run the following command to install all the necessary packages listed in your `package.json`:

    ```bash
    npm install
    ```
## Project Structure
All the Tests are located inside the `Cypress` folder and should have this structure:

### The test logic
This is the part were test suites and test cases can be found, location is the e2e folder.

### The test data
Centralizing the test data for each test suite
Built a separation between the test data of each test system.
All the test data are located in the fixture folder!

### The test automation logic
For the test automation logic, a nomenclature was defined to efficiently navigating through the web interface.
Therefore, an atomic page pattern has been developed in form of page and components objects and located in the support folder.

## Writing Tests
Notice that in this project cypress is configured to do not reset the browser and cookie while navigating from one _"it()"_ to another within a _"describe()"_.

### Basic Test Structure
A basic Cypress test look like this:










## Conclusion
This guide should give you a good starting point for writing automated tests with Cypress. Happy testing!
this is a very helpful Link to solve some cypress's problems https://glebbahmutov.com/blog/tags/cypress/
