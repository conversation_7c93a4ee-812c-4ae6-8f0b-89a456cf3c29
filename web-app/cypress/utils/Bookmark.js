javascript:(function (w, d, v, c, $, s, l, r) {
  if (!($ = w.jQuery) || v > $().jquery || c($)) {
    s = d.createElement("script");
    s.type = 'text/javascript';
    s.src = "https://ajax.googleapis.com/ajax/libs/jquery/" + v + "/jquery.min.js";
    s.onload = s.onreadystatechange = function () {
      if (!l && (!(r = this.readyState) || r === "loaded" || r === "complete")) {
        c(($ = w.jQuery).noConflict(1), l = 1);
        $([s]).remove();
      }
    };
    d.documentElement.childNodes[0].appendChild(s);
  }
})(window, document, "3.7.1", function ($) {
  $('head').append('<style>.tooltip{display:none;position:absolute;border:1px solid #333;background-color:#161616;border-radius:5px;padding:10px;color:#fff;font-size:10px;font-family:"arial"}</style>');
  let dataQaTooltip = function (selector) {
    selector.css('border', 'solid 1px red').hover(function (e) {
      e.preventDefault();
      let title = 'data-testid: ' + this.dataset.testid;
      $(this).data('tipText', title).removeAttr('title');
      $('<p class="tooltip"></p>').text(title).appendTo('body').fadeIn('slow');
      $(this).on('contextmenu', function (e) {
        e.preventDefault();
        prompt('You can use this selector for this element:', '[data-testid="' + this.dataset.testid + '"]');
        e.stopImmediatePropagation();
      });
    }, function () {
      $(this).attr('title', $(this).data('tipText'));
      $('.tooltip').remove();
    }).mousemove(function (e) {
      e.preventDefault();
      $('.tooltip').css({top: e.pageY + 10, left: e.pageX + 20});
    });
  };
  dataQaTooltip($('*[data-testid]'));
  $(document).ajaxComplete(function () {
    dataQaTooltip($('*[data-testid]'));
  });
});
