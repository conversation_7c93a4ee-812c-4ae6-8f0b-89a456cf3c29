export class DigitalDocumentCo{
    private readonly DOCUMENTS_PAGE: string = '[data-testid="digitize-tab-documents"]';
    private readonly DOCUMENTS_BOX: string = '[data-testid="card-component"]';
    private readonly DOCUMENTS_DESC_ITEM: string = '[data-testid="payment-item-main-area"]';
    private readonly DOCUMENTS_BUTTON_ITEM: string = '[data-testid="payment-item-action-area"]';
    private readonly DOCUMENTS_DIALOG: string = '[data-test-id="dialog"]';
    private readonly DOCUMENTS_DIALOG_EMAIL: string = '[data-testid="send-documents-dialog-email-input"]';
    private readonly DOCUMENTS_DIALOG_EMAIL_CONFIRM: string = '[data-testid="send-documents-dialog-email-confirm-input"]';
    private readonly DOCUMENTS_DIALOG_SEND_BUTTON: string = '[data-testid="send-documents-dialog-send-button"]';
    private readonly DOCUMENTS_INPUT: string = '[data-testid="input"]';
    private readonly DOCUMENTS_SUCCESSFULLY_SENT: string = '.cdk-overlay-container'
    private readonly DOCUMENTS_TAX_PAYMENT_PAGE: string = '[data-testid="pay-tax-page"]';
    private readonly DOCUMENTS_VACATION_PAGE: string = '.ng-star-inserted'

    public getDocumentsPage():Cypress.Chainable{
        return cy.get(this.DOCUMENTS_PAGE)
    }

    public getVacationDocumentsPage():Cypress.Chainable{
        return cy.get(this.DOCUMENTS_VACATION_PAGE)
    }

    public getDocumentsBox():Cypress.Chainable{
        return cy.get(this.DOCUMENTS_BOX)
    }

    public getDocumentsDescription():Cypress.Chainable{
        return cy.get(this.DOCUMENTS_DESC_ITEM)
    }

    public getDocumentsActions():Cypress.Chainable{
        return cy.get(this.DOCUMENTS_BUTTON_ITEM)
    }

    public getDocumentsDialog():Cypress.Chainable{
        return cy.get(this.DOCUMENTS_DIALOG)
    }

    public getDocumentsEmail():Cypress.Chainable{
        return this.getDocumentsDialog().find(this.DOCUMENTS_DIALOG_EMAIL)
    }

    public getDocumentsEmailInput():Cypress.Chainable{
        return this.getDocumentsEmail().find(this.DOCUMENTS_INPUT)
    }

    public getDocumentsEmailConfirm():Cypress.Chainable{
        return this.getDocumentsDialog().find(this.DOCUMENTS_DIALOG_EMAIL_CONFIRM)
    }

    public getDocumentsEmailConfirmInput():Cypress.Chainable{
        return this.getDocumentsEmailConfirm().find(this.DOCUMENTS_INPUT)
    }

    public getDocumentsDialogSend():Cypress.Chainable{
        return this.getDocumentsDialog().find(this.DOCUMENTS_DIALOG_SEND_BUTTON)
    }

    public getDocumentsSuccessContainer():Cypress.Chainable{
        return cy.get(this.DOCUMENTS_SUCCESSFULLY_SENT)
    }

    public getDocumentsTaxPaymentPage():Cypress.Chainable{
        return cy.get(this.DOCUMENTS_TAX_PAYMENT_PAGE)
    }
}