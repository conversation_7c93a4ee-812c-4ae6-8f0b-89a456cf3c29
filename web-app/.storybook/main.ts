import type { StorybookConfig } from '@storybook/angular';

const config: StorybookConfig = {
  stories: [
    {
      titlePrefix: 'Shared/Atoms',
      files: '**/*.stories.ts',
      directory: '../src/app/shared/atoms',
    },
    {
      titlePrefix: 'Shared/Molecules',
      files: '**/*.stories.ts',
      directory: '../src/app/shared/molecules',
    },
    {
      titlePrefix: 'Shared/Organisms',
      files: '**/*.stories.ts',
      directory: '../src/app/shared/organisms',
    },
    {
      titlePrefix: 'Shared/Icons',
      files: '**/*.stories.ts',
      directory: '../src/app/shared/icons',
    },
    {
      titlePrefix: 'Core/Layout',
      files: '**/*.stories.ts',
      directory: '../src/app/core/layout',
    },
    '../src/app/features/**/*.stories.ts',
  ],
  addons: [
    '@storybook/addon-links',
    '@storybook/addon-essentials',
    '@chromatic-com/storybook',
    '@storybook/addon-interactions',
    '@storybook/addon-a11y',
    'storybook-addon-angular-router',
  ],
  framework: {
    name: '@storybook/angular',
    options: {},
  },
  docs: {
    autodocs: true,
  },
  staticDirs: [
    { from: '../src/assets', to: '/assets' },
    { from: '../src/favicon.svg', to: 'favicon.svg' },
  ],
};
export default config;
