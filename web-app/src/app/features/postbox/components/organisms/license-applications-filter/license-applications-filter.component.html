<fish-slider-button-group (changed)="handleSliderButtonChanged($event)" [direction]="direction()">
  <fish-slider-button>
    <div class="flex flex-row items-center gap-2">
      <fish-icon-license-application size="32" />
      <span [innerText]="'postbox.applications.filter.pending' | translate"></span>
    </div>
  </fish-slider-button>
  <fish-slider-button>
    <div class="flex min-w-[210px] flex-row items-center gap-2">
      <fish-icon-license-application-denied size="32" />
      <span [innerText]="'postbox.applications.filter.rejected' | translate"></span>
    </div>
  </fish-slider-button>
</fish-slider-button-group>
