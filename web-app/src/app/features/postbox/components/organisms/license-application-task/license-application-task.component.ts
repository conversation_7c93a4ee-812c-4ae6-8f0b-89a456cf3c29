import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { PostboxLimitedLicenseApplication } from '@digifischdok/ngx-register-sdk';

import { IconDocumentPdfComponent } from '@/app/shared/icons/document-pdf/document-pdf.component';
import { TaskComponent } from '@/app/shared/molecules/task/task.component';
import { PersonFullnamePipe } from '@/app/shared/pipes/person-fullname.pipe';

@Component({
  selector: 'fish-license-application-task',
  imports: [TaskComponent, IconDocumentPdfComponent, TranslateModule, PersonFullnamePipe, DatePipe],
  templateUrl: './license-application-task.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class LicenseApplicationTaskComponent {
  public readonly licenseApplication = input.required<PostboxLimitedLicenseApplication>();
}
