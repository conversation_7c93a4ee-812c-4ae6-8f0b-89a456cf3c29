import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { Router } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';

import { PageContentComponent } from '@/app/core/layout/page-content/page-content.component';
import { SearchBarComponent } from '@/app/features/search/components/molecules/search-bar/search-bar.component';
import { TitleComponent } from '@/app/shared/atoms/title/title.component';

@Component({
  selector: 'fish-homepage',
  templateUrl: './homepage.component.html',
  imports: [CommonModule, PageContentComponent, SearchBarComponent, TitleComponent, TranslateModule],
})
export class HomepageComponent {
  constructor(private readonly router: Router) {}

  protected search(query: string): void {
    this.router.navigate(['register-entries'], {
      queryParams: { search: query },
    });
  }
}
