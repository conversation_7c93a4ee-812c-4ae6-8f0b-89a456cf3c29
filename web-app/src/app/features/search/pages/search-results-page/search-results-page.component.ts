import { CommonModule } from '@angular/common';
import { HttpErrorResponse, HttpStatusCode } from '@angular/common/http';
import { ChangeDetectionStrategy, Component, OnInit, inject, signal } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, finalize, map } from 'rxjs';

import { RegisterEntryService, SearchItem } from '@digifischdok/ngx-register-sdk';

import { PageContentComponent } from '@/app/core/layout/page-content/page-content.component';
import { SearchHistoryStore } from '@/app/core/stores/search-history.store';
import { CreationMenuComponent } from '@/app/features/search/components/molecules/creation-menu/creation-menu.component';
import { SearchBarComponent } from '@/app/features/search/components/molecules/search-bar/search-bar.component';
import { SearchErrorComponent } from '@/app/features/search/components/organisms/search-error/search-error.component';
import { SearchError } from '@/app/features/search/components/organisms/search-error/search-error.models';
import { SearchResultsComponent } from '@/app/features/search/components/organisms/search-results/search-results.component';
import { IconLoaderComponent } from '@/app/shared/icons/loader/loader.component';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';

@Component({
  selector: 'fish-search-results-page',
  imports: [
    SearchErrorComponent,
    SearchResultsComponent,
    SearchBarComponent,
    PageContentComponent,
    CommonModule,
    CreationMenuComponent,
    IconLoaderComponent,
    TranslateModule,
  ],
  templateUrl: './search-results-page.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class SearchResultsPageComponent implements OnInit {
  protected readonly searchResults = signal<SearchItem[]>([]);

  protected readonly resultsLoading = signal<boolean>(true);

  protected readonly searchError = signal<SearchError | undefined>(undefined);

  // Dependencies
  private readonly route = inject(ActivatedRoute);

  private readonly router = inject(Router);

  private readonly registerServiceApi = inject(RegisterEntryService);

  private readonly searchHistoryStore = inject(SearchHistoryStore);

  private readonly serverErrorDialogService: ServerDialogService = inject(ServerDialogService);

  protected get searchQuery(): string {
    return this.route.snapshot.queryParamMap.get('search') ?? '';
  }

  public ngOnInit(): void {
    const paramSearchString = this.route.snapshot.queryParamMap.get('search') ?? '';
    this.searchHistoryStore.setFromSearchPage(true);
    this.updateSearchResults(paramSearchString);
  }

  protected search(searchString: string): void {
    const queryParams = {
      search: searchString,
    };

    this.router.navigate([], {
      relativeTo: this.route,
      queryParams,
      queryParamsHandling: 'merge',
    });

    this.searchHistoryStore.setLastSearchQuery(searchString);
    this.updateSearchResults(searchString);
  }

  private updateSearchResults(search: string) {
    if (!search) {
      this.searchResults.set([]);
      this.searchError.set('no-results');
      this.resultsLoading.set(false);
      return;
    }

    this.resultsLoading.set(true);

    this.registerServiceApi
      .registerEntriesControllerSearch(search, 'response')
      .pipe(
        map((response) => response.body),
        catchError((error: unknown) => {
          if (!(error instanceof HttpErrorResponse)) {
            this.serverErrorDialogService.handleServerError(error);
          }
          if (error.status === HttpStatusCode.NotFound) {
            this.searchError.set('no-results');
          } else if (error.status === HttpStatusCode.BadRequest) {
            this.searchError.set('too-many-results');
          } else {
            this.serverErrorDialogService.handleServerError(error);
          }
          return [];
        }),
        finalize(() => {
          this.resultsLoading.set(false);
        })
      )
      .subscribe((result) => {
        this.searchResults.set(result ?? []);
        this.searchError.set(undefined);
        this.searchHistoryStore.setLastSearchQuery(search);
      });
  }
}
