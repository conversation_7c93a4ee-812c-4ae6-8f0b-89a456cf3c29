import { ChangeDetectionStrategy, Component, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

@Component({
  selector: 'fish-document-overview-header',
  imports: [TranslateModule],
  templateUrl: './document-overview-header.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentOverviewHeaderComponent {
  // Inputs
  public readonly title = input<string>();

  public readonly amount = input<number>();
}
