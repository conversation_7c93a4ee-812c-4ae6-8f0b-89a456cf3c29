import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  InputSignal,
  OnDestroy,
  Signal,
  ViewChild,
  WritableSignal,
  computed,
  inject,
  input,
  isDevMode,
  signal,
} from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { catchError, combineLatest, finalize } from 'rxjs';

import {
  CardOrderService,
  ExtendTaxesResponse,
  FishingLicense,
  IdentificationDocument,
  IdentificationDocumentsMailTemplateType,
  LicenseType,
  OrderCheckCardRequest,
} from '@digifischdok/ngx-register-sdk';

import { CitizenStore, ICitizenProfile } from '@/app/core/stores/citizen.store';
import { ConsentStore } from '@/app/core/stores/consent.store';
import { ProfileHeaderStore } from '@/app/core/stores/profile-header.store';
import { DocumentsStepComponent } from '@/app/features/register-edit/components/organisms/documents-step/documents-step.component';
import { PaymentsStepComponent } from '@/app/features/register-edit/components/organisms/steps/payments-step/payments-step.component';
import { PersonalDataStepComponent } from '@/app/features/register-edit/components/organisms/steps/personal-data-step/personal-data-step.component';
import { PersonalDataStepFormGroup } from '@/app/features/register-edit/components/organisms/steps/personal-data-step/personal-data-step.models';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';
import { TabDisabledPipe } from '@/app/features/register-edit/pipes/tab-disabled.pipe';
import { RequestPaymentsBuilderService } from '@/app/features/register-edit/services/request-payments-builder.service';
import { RequestPersonBuilderService } from '@/app/features/register-edit/services/request-person-builder.service';
import { TabComponent } from '@/app/shared/atoms/tab/tab.component';
import { TabGroupComponent } from '@/app/shared/molecules/tab-group/tab-group.component';
import { ServerDialogService } from '@/app/shared/services/server-dialog.service';

@Component({
  selector: 'fish-order-card-tab-group',
  imports: [
    TabComponent,
    TranslateModule,
    TabGroupComponent,
    PersonalDataStepComponent,
    PaymentsStepComponent,
    DocumentsStepComponent,
    TabDisabledPipe,
  ],
  templateUrl: './order-card-tab-group.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OrderCardTabGroupComponent implements AfterViewInit, OnDestroy {
  // INPUTS
  public readonly fishingLicenseNumber: InputSignal<string> = input.required<string>();

  public readonly registerEntryId: InputSignal<string> = input.required<string>();

  // DEPENDENCIES
  private readonly profileHeaderStore: ProfileHeaderStore = inject(ProfileHeaderStore);

  private readonly consentStore: ConsentStore = inject(ConsentStore);

  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  private readonly cardOrderApi: CardOrderService = inject(CardOrderService);

  private readonly personBuilder: RequestPersonBuilderService = inject(RequestPersonBuilderService);

  private readonly paymentsBuilder: RequestPaymentsBuilderService = inject(RequestPaymentsBuilderService);

  private readonly serverDialogService: ServerDialogService = inject(ServerDialogService);

  // FIELDS
  protected readonly isLoading: WritableSignal<boolean> = signal<boolean>(false);

  protected readonly formSent: WritableSignal<boolean> = signal<boolean>(false);

  protected readonly lastEditableIndex: WritableSignal<number> = signal<number>(0);

  protected readonly documents = signal<IdentificationDocument[]>([]);

  protected readonly licenseType: Signal<LicenseType> = computed<LicenseType>(() => {
    const profile: ICitizenProfile | null = this.citizenStore.profile();

    if (!profile) {
      return LicenseType.Regular; // Return normal license type when profile was not yet loaded in order to not run into any rendering issues
    }

    const fishingLicenses: FishingLicense[] = profile.fishingLicenses ?? [];

    const license: FishingLicense | undefined = fishingLicenses.find((license: FishingLicense) => license.number === this.fishingLicenseNumber());

    if (!license) {
      throw new Error(`Tried ordering a new check card, but no license with license number '${this.fishingLicenseNumber()}' could be found.`);
    }

    return license.type;
  });

  protected readonly IdentificationDocumentsMailTemplateType = IdentificationDocumentsMailTemplateType;

  @ViewChild(TabGroupComponent) private readonly tabGroup!: TabGroupComponent;

  @ViewChild(PaymentsStepComponent) private readonly paymentsStep!: PaymentsStepComponent;

  @ViewChild(PersonalDataStepComponent) private readonly personalDataStep!: EditFormStep<PersonalDataStepFormGroup>;

  public ngAfterViewInit(): void {
    this.setLastEditableIndex();
  }

  public ngOnDestroy(): void {
    this.profileHeaderStore.setHomeButtonType('secondary');
  }

  protected handleSave(): void {
    const registerEntryId = this.registerEntryId();
    if (!registerEntryId) {
      throw new Error('Register ID is missing. Please ensure the register Id was properly assigned before trying to perform a Save Operation');
    }
    const fishingLicenseNumber = this.fishingLicenseNumber();
    if (!fishingLicenseNumber) {
      throw new Error(
        'Fishing License Number is missing. Please ensure the fishing license number was properly assigned before trying to perform a Save Operation'
      );
    }
    const request = this.buildOrderCheckCardRequest();
    this.isLoading.set(true);
    this.cardOrderApi
      .fishingLicenseOrdersControllerOrder(registerEntryId, fishingLicenseNumber, request, undefined)
      .pipe(
        catchError((err: unknown) => this.serverDialogService.handleServerError(err)),
        finalize(() => this.isLoading.set(false))
      )
      .subscribe((response) => this.handleSuccessfulResponse(response));
  }

  private buildOrderCheckCardRequest(): OrderCheckCardRequest {
    return {
      person: this.personBuilder.buildPerson(this.personalDataStep.formGroup.value),
      consentInfo: this.consentStore.getConsentInfo(),
      taxes: this.paymentsBuilder.buildTaxes(this.paymentsStep.formGroup.value),
      fees: this.paymentsBuilder.buildFees(this.licenseType(), this.paymentsStep.formGroup.value),
    };
  }

  private handleSuccessfulResponse(response: ExtendTaxesResponse): void {
    const registerEntryId = response.registerEntryId;
    if (!registerEntryId) {
      throw new Error('Inconsistent server state: registerEntryId is null');
    }
    const documents = response.documents;
    if (documents?.length) {
      this.documents.set(documents);
    }

    this.citizenStore.patchByResponse(response);
    this.profileHeaderStore.setHomeButtonType('primary');
    this.formSent.set(true);

    // Wait for thread execution
    setTimeout(() => this.tabGroup.goToLastTab(), 1);
  }

  private setLastEditableIndex(): void {
    if (isDevMode()) {
      this.lastEditableIndex.set(3);
    } else {
      combineLatest([this.personalDataStep.canContinue$]).subscribe((arr: boolean[]) => {
        const latestValid: number = arr.lastIndexOf(true);
        const firstInvalid: number = arr.indexOf(false);

        const lastEditable: number = firstInvalid >= 0 ? firstInvalid : latestValid + 1;

        this.lastEditableIndex.set(lastEditable);
      });
    }
  }
}
