import { Meta, StoryObj } from '@storybook/angular';

import { PaymentBoxComponent } from '@/app/features/register-edit/components/organisms/payment-box/payment-box.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<PaymentBoxComponent> = {
  title: 'register-edit/PaymentBox',
  component: PaymentBoxComponent,
  args: {
    feeCost: 20,
    previouslyPaidTax: null,
    taxCostOptions: [
      {
        optionType: 'stepper',
        minYears: 1,
        maxYears: 4,
        durationLabel: 'Dauer',
      },
      {
        optionType: 'static',
        durationLabel: 'Maximum',
        years: 4,
      },
    ],
    showFee: true,
  },
  argTypes: {
    feeCost: {
      control: 'number',
    },
    previouslyPaidTax: {
      control: 'object',
    },
    taxCostOptions: {
      control: 'object',
    },
  },
};

export default meta;

type Story = StoryObj<PaymentBoxComponent>;

export const Default: Story = {
  args: {
    showFee: true,
  },
};

export const TaxOnly: Story = {
  args: {
    showFee: false,
  },
};

export const Extended: Story = {
  args: {
    previouslyPaidTax: {
      yearFrom: 2022,
      yearTo: 2024,
      cost: 0,
    },
  },
};
