import { ChangeDetectionStrategy, Component } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { NoticeContentComponent } from '@/app/shared/atoms/notice-content/notice-content.component';
import { NoticeComponent } from '@/app/shared/molecules/notice/notice.component';

@Component({
  selector: 'fish-delete-person-notices',
  imports: [NoticeComponent, NoticeContentComponent, TranslateModule],
  templateUrl: './delete-person-notices.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DeletePersonNoticesComponent {}
