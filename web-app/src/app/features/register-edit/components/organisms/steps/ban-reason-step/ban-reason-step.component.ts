import { AfterViewInit, ChangeDetectionStrategy, Component, OutputEmitterRef, ViewChild, effect, inject, output } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';
import { BehaviorSubject, distinctUntilChanged } from 'rxjs';

import { CitizenStore } from '@/app/core/stores/citizen.store';
import { BanReasonFormComponent } from '@/app/features/register-edit/components/organisms/ban-reason-form/ban-reason-form.component';
import { BanReasonFormGroup } from '@/app/features/register-edit/components/organisms/ban-reason-form/ban-reason-form.models';
import { EditFooterComponent } from '@/app/features/register-edit/components/organisms/edit-footer/edit-footer.component';
import { EditFormStep } from '@/app/features/register-edit/interfaces/edit-form-step';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { CardComponent } from '@/app/shared/molecules/card/card.component';

@Component({
  selector: 'fish-ban-reason-step',
  imports: [CardComponent, CardHeaderComponent, CardContentComponent, BanReasonFormComponent, EditFooterComponent, TranslateModule],
  templateUrl: './ban-reason-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BanReasonStepComponent implements AfterViewInit, EditFormStep<BanReasonFormGroup> {
  // Fields
  public readonly continueButtonClicked: OutputEmitterRef<void> = output();

  public formGroup!: BanReasonFormGroup;

  public canContinue$: BehaviorSubject<boolean> = new BehaviorSubject(false);

  @ViewChild(BanReasonFormComponent) private readonly banReasonForm!: BanReasonFormComponent;

  // Dependencies
  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  constructor() {
    this.initCitizenStoreEffect();
  }

  public ngAfterViewInit(): void {
    this.formGroup = this.banReasonForm.formGroup;
    this.formGroup.statusChanges.pipe(distinctUntilChanged()).subscribe(() => this.canContinue$.next(this.formGroup.valid));
  }

  private initCitizenStoreEffect(): void {
    effect(() => {
      const { ban } = this.citizenStore.profile() ?? {};
      if (ban) {
        this.formGroup.patchValue({ ...ban });
        this.banReasonForm.captureInitialState();
        this.formGroup.updateValueAndValidity();
      }
    });
  }

  protected onContinue(): void {
    this.banReasonForm.validate();
    if (this.formGroup.valid) {
      this.continueButtonClicked.emit();
    }
  }
}
