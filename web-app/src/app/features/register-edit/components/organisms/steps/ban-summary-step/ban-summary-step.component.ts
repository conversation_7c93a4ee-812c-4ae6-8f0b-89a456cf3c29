import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, Signal, computed, inject } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { Ban, Person } from '@digifischdok/ngx-register-sdk';

import { CitizenStore } from '@/app/core/stores/citizen.store';
import { TableCellComponent } from '@/app/shared/atoms/table-cell/table-cell.component';
import { TableHeaderComponent } from '@/app/shared/atoms/table-header/table-header.component';
import { TableRowComponent } from '@/app/shared/atoms/table-row/table-row.component';
import { IconNoLicenseCardComponent } from '@/app/shared/icons/no-license-card/no-license-card.component';
import { PersonFullnamePipe } from '@/app/shared/pipes/person-fullname.pipe';

@Component({
  selector: 'fish-ban-summary-step',
  imports: [IconNoLicenseCardComponent, TableHeaderComponent, TableRowComponent, TableCellComponent, DatePipe, PersonFullnamePipe, TranslateModule],
  templateUrl: './ban-summary-step.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BanSummaryStepComponent {
  private readonly citizenStore = inject(CitizenStore);

  protected readonly citizen: Signal<Person | undefined> = computed(() => {
    return this.citizenStore.profile()?.person;
  });

  protected readonly ban: Signal<Ban | undefined> = computed(() => {
    return this.citizenStore.profile()?.ban;
  });
}
