import { FormControl, FormGroup } from '@angular/forms';

export type VacationLicenseValidityPeriodStep = {
  /**
   * Duration selected for the validity period.
   *
   * Note that depending on the federal state, duration may have different units (day, months, years)
   */
  duration: FormControl<string>;
  /**
   * Beginning of the validity period.
   */
  validFrom: FormControl<string | null>;
  /**
   * Disabled form field, the value will be calculated automatically and can be read from this form field.
   */
  validTo: FormControl<string | null>;
};

export type VacationLicenseValidityPeriodStepFormGroup = FormGroup<VacationLicenseValidityPeriodStep>;

export type VacationLicenseValidityPeriodStepFormValues = VacationLicenseValidityPeriodStepFormGroup['value'];
