import { FormControl, FormGroup } from '@angular/forms';

export type NoPermanentResidenceFormGroup = FormGroup<{
  city: FormControl<string | null>;
  street: FormControl<string | null>;
  streetNumber: FormControl<string | null>;
  postcode: FormControl<string | null>;
  detail: FormControl<string | null>;
  office: FormControl<string | null>;
  deliverTo: FormControl<string | null>;
}>;

export type NoPermanentResidenceFormValues = NoPermanentResidenceFormGroup['value'];
