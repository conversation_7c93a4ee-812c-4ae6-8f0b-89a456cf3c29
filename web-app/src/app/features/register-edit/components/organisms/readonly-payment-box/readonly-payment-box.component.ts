import { ChangeDetectionStrategy, Component, OnInit, computed, effect, inject, input } from '@angular/core';
import { FormBuilder } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';

import { LicenseType } from '@digifischdok/ngx-register-sdk';

import { UserService } from '@/app/core/services/user/user.service';
import { CitizenStore } from '@/app/core/stores/citizen.store';
import { FeePaymentItemComponent } from '@/app/features/register-edit/components/molecules/fee-payment-item/fee-payment-item.component';
import { PayedTaxesPaymentItemComponent } from '@/app/features/register-edit/components/molecules/payed-taxes-payment-item/payed-taxes-payment-item.component';
import { PaymentBoxFormGroup } from '@/app/features/register-edit/components/organisms/payment-box/payment-box.models';
import { ReadonlyTaxPaymentItemComponent } from '@/app/features/register-edit/components/organisms/readonly-tax-payment-item/readonly-tax-payment-item.component';
import { ValidityPeriodService } from '@/app/features/register-edit/services/validity-period-service.service';
import { CardContentComponent } from '@/app/shared/atoms/card-content/card-content.component';
import { CardHeaderComponent } from '@/app/shared/atoms/card-header/card-header.component';
import { FormComponent } from '@/app/shared/atoms/form/form.component';
import { IconMoneyComponent } from '@/app/shared/icons/money/money.component';
import { TaxSelection } from '@/app/shared/models/tax-selection';
import { CardComponent } from '@/app/shared/molecules/card/card.component';
import { CostsService } from '@/app/shared/services/costs/costs.service';

@Component({
  selector: 'fish-readonly-payment-box',
  imports: [
    CardComponent,
    CardContentComponent,
    CardHeaderComponent,
    FeePaymentItemComponent,
    IconMoneyComponent,
    PayedTaxesPaymentItemComponent,
    TranslateModule,
    ReadonlyTaxPaymentItemComponent,
  ],
  templateUrl: './readonly-payment-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ReadonlyPaymentBoxComponent extends FormComponent<PaymentBoxFormGroup> implements OnInit {
  // Inputs

  public readonly showFee = input<boolean>(true);

  public readonly feeCost = input<number>(0);

  public readonly licenseType = input<LicenseType>(LicenseType.Regular);

  public readonly selectedValidityPeriod = input<
    | {
        validFrom: string;
        validTo: string;
      }
    | undefined
  >(undefined);

  // Fields

  protected readonly totalAmount = computed<number>(() => (this.taxCost() ?? 0) + (this.showFee() ? this.feeCost() : 0));

  private readonly selectedValidityPeriodYears = computed<{ yearFrom: number; yearTo: number }>(() => {
    const yearFrom = new Date(this.selectedValidityPeriod()?.validFrom ?? 0).getFullYear();
    const yearTo = new Date(this.selectedValidityPeriod()?.validTo ?? 0).getFullYear();
    return { yearFrom, yearTo };
  });

  protected readonly mergedTaxValidityPeriodsForFederalState = computed<{ yearFrom: number; yearTo: number }[]>(() => {
    const taxes = this.citizenStore.profile()?.taxes?.filter((tax) => tax.federalState === this.userService.getFederalState());
    if (!taxes) return [];

    return this.validityPeriodService.mergeIntervals(
      taxes.map((tax) => ({
        yearFrom: new Date(tax.validFrom).getFullYear(),
        yearTo: new Date(tax.validTo!).getFullYear(),
      }))
    );
  });

  protected readonly previouslyPaidTax = computed<TaxSelection | null>(() => {
    const yearFrom = this.selectedValidityPeriodYears().yearFrom;
    const yearTo = this.selectedValidityPeriodYears().yearTo;
    const firstOverlappingTax = this.mergedTaxValidityPeriodsForFederalState().find((tax) => !(yearTo < tax.yearFrom || yearFrom > tax.yearTo));
    if (!firstOverlappingTax) return null;
    return {
      yearFrom: firstOverlappingTax.yearFrom,
      yearTo: firstOverlappingTax.yearTo,
      cost: 0,
    };
  });

  protected readonly unpaidValidityPeriod = computed(() => {
    let yearFrom = this.selectedValidityPeriodYears().yearFrom;
    let yearTo = this.selectedValidityPeriodYears().yearTo;

    const previousTax = this.previouslyPaidTax();

    if (previousTax) {
      // If the selected validity period is completely covered by a previous tax, reset the tax selection
      if (previousTax.yearFrom <= yearFrom && previousTax.yearTo && previousTax.yearTo! >= yearTo) {
        return null;
      } else {
        // If the previous tax overlaps either with the start or the end of the selected validity period, adjust the tax selection
        if (previousTax.yearFrom <= yearFrom && previousTax.yearTo! < yearTo) {
          yearFrom = previousTax.yearTo! + 1;
        } else if (previousTax.yearFrom > yearFrom && previousTax.yearTo! >= yearTo) {
          yearTo = previousTax.yearFrom - 1;
        }
      }
    }
    return { yearFrom, yearTo };
  });

  protected readonly taxCost = computed(() => {
    const unpaidValidityPeriod = this.unpaidValidityPeriod();
    if (!unpaidValidityPeriod || !unpaidValidityPeriod.yearFrom || !unpaidValidityPeriod.yearTo) return null;
    return this.costsService.calculateTaxCost(unpaidValidityPeriod.yearTo - unpaidValidityPeriod.yearFrom + 1, this.showFee());
  });

  public override formGroup!: PaymentBoxFormGroup;

  private readonly formBuilder: FormBuilder = inject(FormBuilder);

  private readonly citizenStore: CitizenStore = inject(CitizenStore);

  private readonly userService: UserService = inject(UserService);

  private readonly costsService: CostsService = inject(CostsService);

  private readonly validityPeriodService: ValidityPeriodService = inject(ValidityPeriodService);

  constructor() {
    super();
    this.initFormGroup();
    this.initValidityPeriodEffect();
    this.captureInitialState();
  }

  public override ngOnInit(): void {
    super.ngOnInit();
  }

  private initFormGroup(): void {
    this.formGroup = this.formBuilder.group({
      taxSelection: this.formBuilder.control<TaxSelection | null>(null),
    });
    this.formGroup.controls.taxSelection.valueChanges.subscribe(() => this.formGroup.updateValueAndValidity());
  }

  private initValidityPeriodEffect(): void {
    effect(() => {
      const unpaidValidityPeriod = this.unpaidValidityPeriod();
      const taxCost = this.taxCost();
      if (unpaidValidityPeriod && taxCost) {
        this.formGroup.controls.taxSelection.setValue({
          yearFrom: unpaidValidityPeriod.yearFrom,
          yearTo: unpaidValidityPeriod.yearTo,
          cost: taxCost,
        });
      } else {
        this.formGroup.controls.taxSelection.setValue(null);
      }
    });
  }
}
