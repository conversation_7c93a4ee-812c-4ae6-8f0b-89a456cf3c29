@if (thirdPartySubmissionControl() && !showThirdPartySubmissionControlInsideFooter()) {
  <fish-confirm-box
    class="h-16"
    [formControl]="thirdPartySubmissionControl()!"
    [label]="'third_party_consent.label' | translate"
    [subLabel]="'third_party_consent.sub_label' | translate"
    data-testid="edit-footer-confirm-box"
  />
}
<div [class]="containerClasses()" data-testid="edit-footer-component">
  @if (showBackButton()) {
    <fish-button data-testid="edit-footer-back-button" type="secondary" iconPosition="left" size="l" (clicked)="backed.emit()">
      <fish-icon-arrow-left icon size="48" data-testid="edit-footer-back-icon" />
      <span [innerText]="'common.button.back' | translate"></span>
    </fish-button>
  }

  @if (thirdPartySubmissionControl() && showThirdPartySubmissionControlInsideFooter()) {
    <fish-confirm-box
      class="h-16"
      [formControl]="thirdPartySubmissionControl()!"
      [label]="'third_party_consent.label' | translate"
      [subLabel]="'third_party_consent.sub_label' | translate"
      data-testid="edit-footer-confirm-box"
    />
  }

  @if (isLastStep()) {
    <fish-button
      data-testid="edit-footer-finnish-button"
      type="primary"
      iconPosition="left"
      size="l"
      (clicked)="this.continued.emit()"
      [loading]="isLoading()"
    >
      <fish-icon-check icon size="48" data-testid="edit-footer-finnish-icon"></fish-icon-check>
      <span [innerText]="continueButtonLabel() ?? 'common.button.finnish' | translate"></span>
    </fish-button>
  } @else {
    <fish-button
      data-testid="edit-footer-continue-button"
      type="primary"
      iconPosition="right"
      size="l"
      (clicked)="this.continued.emit()"
      [loading]="isLoading()"
    >
      <fish-icon-arrow-right icon size="48" data-testid="edit-footer-continue-icon"></fish-icon-arrow-right>
      <span [innerText]="continueButtonLabel() ?? 'common.button.confirm_and_continue' | translate"></span>
    </fish-button>
  }
</div>
