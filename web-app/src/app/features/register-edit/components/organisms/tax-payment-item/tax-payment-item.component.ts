import { ChangeDetectionStrategy, Component, OnInit, ViewChild, input, signal } from '@angular/core';
import { Form<PERSON>uilder, FormControl } from '@angular/forms';

import { TranslateModule } from '@ngx-translate/core';

import { TaxesSelectDialogComponent } from '@/app/features/register-edit/components/organisms/taxes-select-dialog/taxes-select-dialog.component';
import { TaxDialogSelection } from '@/app/features/register-edit/components/organisms/taxes-select-dialog/taxes-select-dialog.models';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { PaymentItemActionAreaComponent } from '@/app/shared/atoms/payment-item-action-area/payment-item-action-area.component';
import { PaymentItemMainAreaComponent } from '@/app/shared/atoms/payment-item-main-area/payment-item-main-area.component';
import { IconEditComponent } from '@/app/shared/icons/edit/edit.component';
import { TaxSelection } from '@/app/shared/models/tax-selection';
import { PaymentItemComponent } from '@/app/shared/molecules/payment-item/payment-item.component';
import { FormatCurrencyPipe } from '@/app/shared/pipes/format-currency.pipe';
import { TaxCostOption } from '@/app/shared/services/costs/costs.models';

@Component({
  selector: 'fish-tax-payment-item',
  imports: [
    PaymentItemComponent,
    PaymentItemMainAreaComponent,
    PaymentItemActionAreaComponent,
    FormatCurrencyPipe,
    ButtonComponent,
    TranslateModule,
    IconEditComponent,
    TaxesSelectDialogComponent,
  ],
  templateUrl: './tax-payment-item.component.html',
  changeDetection: ChangeDetectionStrategy.Default,
})
export class TaxPaymentItemComponent implements OnInit {
  // Inputs
  public readonly costOptions = input<TaxCostOption[]>([]);

  public readonly taxSelectionControl = input.required<FormControl<TaxSelection | null>>();

  public readonly isTaxOptional = input<boolean>(false);

  public readonly officeFeeAlreadyPaid = input<boolean>(true);

  // Fields

  protected readonly initialSelection = signal<number | null>(null);

  public readonly isSelectedControl: FormControl<boolean>;

  @ViewChild(TaxesSelectDialogComponent) private readonly dialog!: TaxesSelectDialogComponent;

  constructor(formBuilder: FormBuilder) {
    this.isSelectedControl = formBuilder.nonNullable.control(false);
  }

  public ngOnInit(): void {
    // Set initial values
    this.initialSelection.set(0); // by default is the first value

    this.isSelectedControl.valueChanges.subscribe((value) => {
      if (value) {
        this.openDialog();
      } else {
        this.taxSelectionControl().setValue(null);
      }
    });
  }

  protected openDialog(): void {
    this.dialog.openDialog();
  }

  protected handleDialogConfirmed(selection: TaxDialogSelection): void {
    if (!selection?.tax) {
      throw new Error('the taxes select dialog was confirmed using wrong values');
    }

    const newCost = selection.tax.cost;

    this.taxSelectionControl().setValue({
      ...selection.tax,
      cost: newCost,
    });
  }

  protected handleDialogClosed(): void {
    if (!this.taxSelectionControl().value) {
      this.isSelectedControl.setValue(false);
    }
  }
}
