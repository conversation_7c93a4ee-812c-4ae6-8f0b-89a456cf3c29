<fish-payment-item data-testid="tax-item">
  <fish-payment-item-main-area [control]="isSelectedControl">
    <div class="flex flex-col leading-snug">
      <div class="text-base font-bold">
        @if (taxSelectionControl().value) {
          <span [innerText]="'edit_form.payments.tax.title_selected' | translate"></span>
        } @else {
          <span [innerText]="'edit_form.payments.tax.title' | translate"></span>
        }
      </div>
      <div class="text-s">
        @if (taxSelectionControl().value) {
          <div class="font-bold">
            <span [innerText]="taxSelectionControl().value!.yearFrom"></span>
            —
            <span [innerText]="taxSelectionControl().value!.yearTo ?? ('edit_form.payments.tax.indefinitely' | translate)"></span>
          </div>
        } @else if (isTaxOptional()) {
          <span class="font-bold text-font-secondary" [innerText]="'common.optional' | translate"></span>
        }
      </div>
    </div>
  </fish-payment-item-main-area>
  <fish-payment-item-action-area>
    @if (taxSelectionControl().value) {
      <div class="pr-6">
        <fish-button type="secondary" size="m" (clicked)="openDialog()" data-testid="tax-item-edit-button">
          <fish-icon-edit size="32" icon></fish-icon-edit>
          <span [innerText]="'common.button.edit' | translate"></span>
        </fish-button>
      </div>
    }
    <div class="w-24 text-end text-base">
      <span [innerText]="taxSelectionControl().value?.cost | formatCurrency"></span>
    </div>
  </fish-payment-item-action-area>
</fish-payment-item>
<fish-taxes-select-dialog
  (confirmed)="handleDialogConfirmed($event)"
  (closed)="handleDialogClosed()"
  [costOptions]="costOptions()"
  [initialSelection]="initialSelection()"
  [officeFeeAlreadyPaid]="officeFeeAlreadyPaid()"
></fish-taxes-select-dialog>
