import { AsyncPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { Tax } from '@digifischdok/ngx-register-sdk';

import { FederalStateGroupedByFederalState } from '@/app/features/service-overview/components/molecules/foreign-tax-display/foreign-tax-display.models';
import { DisplayTaxPeriodPipePipe } from '@/app/features/service-overview/pipes/display-tax-period.pipe';
import { BadgeComponent } from '@/app/shared/atoms/badge/badge.component';
import { IconInfoComponent } from '@/app/shared/icons/info/info.component';
import { DisplayFederalStatePipe } from '@/app/shared/pipes/display-federal-state.pipe';

@Component({
  selector: 'fish-foreign-tax-display',
  imports: [BadgeComponent, DisplayTaxPeriodPipePipe, TranslateModule, IconInfoComponent, DisplayFederalStatePipe, AsyncPipe],
  templateUrl: './foreign-tax-display.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ForeignTaxDisplayComponent {
  public readonly taxes = input<Tax[]>([]);

  protected readonly taxesPerFederalState = computed<FederalStateGroupedByFederalState>(() => {
    return this.taxes().sort(this.compareByValidFrom).reduce(this.groupByFederalState, []).sort(this.compareByFederalState);
  });

  private groupByFederalState(previous: FederalStateGroupedByFederalState, currentTax: Tax): FederalStateGroupedByFederalState {
    const federalStateInPrevious = previous.find((grouping) => grouping.federalState === currentTax.federalState);
    if (!federalStateInPrevious) {
      return [...previous, { federalState: currentTax.federalState, taxes: [currentTax] }];
    }

    federalStateInPrevious.taxes = [...federalStateInPrevious.taxes, currentTax];
    const previousWithoutCurrent = previous.filter((grouping) => grouping.federalState !== currentTax.federalState);

    return [...previousWithoutCurrent, federalStateInPrevious];
  }

  private compareByFederalState(groupA: { federalState: string }, groupB: { federalState: string }): number {
    return groupA.federalState.localeCompare(groupB.federalState);
  }

  // Sort taxes by validFrom in ascending order
  private compareByValidFrom(taxA: Tax, taxB: Tax): number {
    return new Date(taxA.validFrom).getTime() - new Date(taxB.validFrom).getTime();
  }
}
