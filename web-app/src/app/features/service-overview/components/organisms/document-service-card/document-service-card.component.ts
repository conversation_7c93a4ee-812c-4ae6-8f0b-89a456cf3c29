import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

import { IdentificationDocument } from '@digifischdok/ngx-register-sdk';

import { ServiceCardContentComponent } from '@/app/features/service-overview/components/atoms/service-card-content/service-card-content.component';
import { ServiceCardFooterComponent } from '@/app/features/service-overview/components/atoms/service-card-footer/service-card-footer.component';
import { ServiceCardHeaderComponent } from '@/app/features/service-overview/components/atoms/service-card-header/service-card-header.component';
import { ServiceCardComponent } from '@/app/features/service-overview/components/molecules/service-card/service-card.component';
import { ServiceCardButtonDirective } from '@/app/features/service-overview/directives/service-card-button.directive';
import { InspectionLinkComponent } from '@/app/shared/atoms/inspection-link/inspection-link.component';
import { IconCertificateComponent } from '@/app/shared/icons/certificate/certificate.component';
import { IconDigitalDocumentsComponent } from '@/app/shared/icons/digitaldocuments/digitaldocuments.component';
import { IconFishingTaxComponent } from '@/app/shared/icons/fisching-tax/fishing-tax.component';
import { IconLicenseCardComponent } from '@/app/shared/icons/license-card/license-card.component';
import { isLimitedLicenseApprovalDocument, isTaxPDFDocument, isVisibleLicensePDFDocument } from '@/app/shared/utils/document.utils';

@Component({
  selector: 'fish-document-service-card',
  imports: [
    ServiceCardComponent,
    ServiceCardHeaderComponent,
    ServiceCardContentComponent,
    ServiceCardFooterComponent,
    IconDigitalDocumentsComponent,
    TranslateModule,
    IconFishingTaxComponent,
    IconLicenseCardComponent,
    ServiceCardButtonDirective,
    InspectionLinkComponent,
    IconCertificateComponent,
  ],
  templateUrl: './document-service-card.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DocumentServiceCardComponent {
  // Inputs
  public readonly documents = input.required<IdentificationDocument[]>();

  // Fields
  protected readonly taxDocumentCount = computed(() => this.documents().filter((document) => isTaxPDFDocument(document)).length);

  protected readonly licenseDocumentCount = computed(() => this.documents().filter((document) => isVisibleLicensePDFDocument(document)).length);

  protected readonly limitedLicenseApprovalDocumentCount = computed(
    () => this.documents().filter((document) => isLimitedLicenseApprovalDocument(document)).length
  );
}
