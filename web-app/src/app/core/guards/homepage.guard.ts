import { inject } from '@angular/core';
import { CanActivateFn, Router } from '@angular/router';

import { KeycloakService } from 'keycloak-angular';

import { UserRole } from '@/app/core/services/user/user.constants';

/**
 * Guard that redirects users to different homepages based on their role.
 *
 * @returns { Promise<boolean> } - Returns a promise that resolves to `false` if the redirection fails, or `true` if redirection succeeds or is not needed.
 */
export const homepageGuard: CanActivateFn = async (): Promise<boolean> => {
  const router: Router = inject(Router);
  const service: KeycloakService = inject(KeycloakService);

  if (service.isUserInRole(UserRole.ExamDataCreator)) {
    return router.navigate(['/create-passed-exam']);
  }

  return true;
};
