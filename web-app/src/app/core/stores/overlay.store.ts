import { patchState, signalStore, withMethods, withState } from '@ngrx/signals';

type OverlayState = {
  isShown: boolean;
};

const initialState: OverlayState = {
  isShown: false,
};

export const OverlayStore = signalStore(
  { providedIn: 'root' },
  withState(initialState),
  withMethods((store) => ({
    setShown(value: boolean) {
      patchState(store, { isShown: value });
    },
  }))
);

export type OverlayStore = InstanceType<typeof OverlayStore>; // so can it be via constructor injected
