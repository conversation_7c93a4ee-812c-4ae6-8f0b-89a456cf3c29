import { ChangeDetectionStrategy, Component, HostBinding } from '@angular/core';

import { TranslateModule } from '@ngx-translate/core';

@Component({
  imports: [TranslateModule],
  templateUrl: './background-wave-pattern.component.html',
  selector: 'fish-background-wave-pattern',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BackgroundWavePatternComponent {
  @HostBinding('class')
  public hostClass = 'odd:left-[66px] absolute';
}
