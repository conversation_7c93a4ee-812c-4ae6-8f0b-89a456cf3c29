import { Injectable, inject } from '@angular/core';

import { S3StorageService } from '@digifischdok/ngx-register-sdk';

@Injectable({
  providedIn: 'root',
})
export class S3FileDownloadService {
  private static readonly OBJECT_URL_TIMEOUT = 60 * 1000; // 60 seconds

  private readonly s3StorageService = inject(S3StorageService);

  public openPdfInNewTab(filePath: string): void {
    this.s3StorageService.s3ControllerGet(filePath).subscribe((blob) => {
      if (blob && blob.type === 'application/pdf') {
        const pdfUrl = window.URL.createObjectURL(blob);
        window.open(pdfUrl, '_blank');

        setTimeout(() => {
          window.URL.revokeObjectURL(pdfUrl);
        }, S3FileDownloadService.OBJECT_URL_TIMEOUT);
      } else {
        console.error('The downloaded file is not a valid PDF.');
      }
    });
  }
}
