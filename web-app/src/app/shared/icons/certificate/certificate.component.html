@if (size === "32") {
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="32"
    height="32"
    fill="currentColor"
    viewBox="0 0 32 32"
  >
    <path
      fill-rule="evenodd"
      d="M21 24V13h-5V8h-5v16h10ZM18 8.828 20.172 11H18V8.828ZM10 6a1 1 0 0 0-1 1v18a1 1 0 0 0 1 1h12a1 1 0 0 0 1-1V11.414a1 1 0 0 0-.293-.707l-4.414-4.414A1 1 0 0 0 17.586 6H10Zm5.707 14.707 4-4-1.414-1.414L15 18.586l-1.293-1.293-1.414 1.414 2 2 .707.707.707-.707Z"
      clip-rule="evenodd"
    />
  </svg>
} @else if (size === "64") {
  <svg width="96" height="122" viewBox="0 0 96 122" fill="none" xmlns="http://www.w3.org/2000/svg">
    <g filter="url(#filter0_bddddi_1097_5755)">
      <path d="M37 45H50V84L43.5 80L37 84V45Z" fill="#163BBF" fill-opacity="0.8"/>
    </g>
    <g filter="url(#filter1_bddddi_1097_5755)">
      <path d="M46 45H59V88L52.5 84L46 88V45Z" fill="#163BBF" fill-opacity="0.8"/>
    </g>
    <g filter="url(#filter2_bddddi_1097_5755)">
      <path
        d="M45.1702 10.1137C46.4011 7.88936 49.5989 7.88936 50.8298 10.1137C51.7987 11.8647 54.1093 12.3243 55.6746 11.0774C57.663 9.49341 60.6173 10.7171 60.9033 13.2432C61.1284 15.2317 63.0873 16.5405 65.0106 15.9875C67.4537 15.2851 69.7149 17.5463 69.0125 19.9894C68.4595 21.9127 69.7683 23.8716 71.7568 24.0967C74.2829 24.3827 75.5066 27.337 73.9226 29.3254C72.6757 30.8907 73.1353 33.2013 74.8863 34.1702C77.1106 35.4011 77.1106 38.5989 74.8863 39.8298C73.1353 40.7987 72.6757 43.1093 73.9226 44.6746C75.5066 46.663 74.2829 49.6173 71.7568 49.9033C69.7683 50.1284 68.4595 52.0873 69.0125 54.0106C69.7149 56.4537 67.4537 58.7149 65.0106 58.0125C63.0873 57.4595 61.1284 58.7683 60.9033 60.7568C60.6173 63.2829 57.663 64.5066 55.6746 62.9226C54.1093 61.6757 51.7987 62.1353 50.8298 63.8863C49.5989 66.1106 46.4011 66.1106 45.1702 63.8863C44.2013 62.1353 41.8907 61.6757 40.3254 62.9226C38.337 64.5066 35.3827 63.2829 35.0967 60.7568C34.8716 58.7683 32.9127 57.4595 30.9894 58.0125C28.5463 58.7149 26.2851 56.4537 26.9875 54.0106C27.5405 52.0873 26.2317 50.1284 24.2432 49.9033C21.7171 49.6173 20.4934 46.663 22.0774 44.6746C23.3243 43.1093 22.8647 40.7987 21.1137 39.8298C18.8894 38.5989 18.8894 35.4011 21.1137 34.1702C22.8647 33.2013 23.3243 30.8907 22.0774 29.3254C20.4934 27.337 21.7171 24.3827 24.2432 24.0967C26.2317 23.8716 27.5405 21.9127 26.9875 19.9894C26.2851 17.5463 28.5463 15.2851 30.9894 15.9875C32.9127 16.5405 34.8716 15.2317 35.0967 13.2432C35.3827 10.7171 38.337 9.49341 40.3254 11.0774C41.8907 12.3243 44.2013 11.8647 45.1702 10.1137Z"
        fill="white" fill-opacity="0.24"/>
    </g>
    <g filter="url(#filter3_bddddi_1097_5755)">
      <path
        d="M46.3198 21.0362C47.0507 19.7156 48.9493 19.7156 49.6802 21.0362C50.2555 22.0759 51.6274 22.3488 52.5568 21.6084C53.7374 20.668 55.4915 21.3946 55.6613 22.8944C55.795 24.0751 56.9581 24.8522 58.1 24.5239C59.5507 24.1068 60.8932 25.4493 60.4761 26.9C60.1478 28.0419 60.9249 29.205 62.1056 29.3387C63.6054 29.5085 64.332 31.2626 63.3916 32.4432C62.6512 33.3726 62.9241 34.7445 63.9638 35.3198C65.2844 36.0507 65.2844 37.9493 63.9638 38.6802C62.9241 39.2555 62.6512 40.6274 63.3916 41.5568C64.332 42.7374 63.6054 44.4915 62.1056 44.6613C60.9249 44.795 60.1478 45.9581 60.4761 47.1C60.8932 48.5507 59.5507 49.8932 58.1 49.4761C56.9581 49.1478 55.795 49.9249 55.6613 51.1056C55.4915 52.6054 53.7374 53.332 52.5568 52.3916C51.6274 51.6512 50.2555 51.9241 49.6802 52.9638C48.9493 54.2844 47.0507 54.2844 46.3198 52.9638C45.7445 51.9241 44.3726 51.6512 43.4432 52.3916C42.2626 53.332 40.5085 52.6054 40.3387 51.1056C40.205 49.9249 39.0419 49.1478 37.9 49.4761C36.4493 49.8932 35.1068 48.5507 35.5239 47.1C35.8522 45.9581 35.0751 44.795 33.8944 44.6613C32.3946 44.4915 31.668 42.7374 32.6084 41.5568C33.3488 40.6274 33.0759 39.2555 32.0362 38.6802C30.7156 37.9493 30.7156 36.0507 32.0362 35.3198C33.0759 34.7445 33.3488 33.3726 32.6084 32.4432C31.668 31.2626 32.3946 29.5085 33.8944 29.3387C35.0751 29.205 35.8522 28.0419 35.5239 26.9C35.1068 25.4493 36.4493 24.1068 37.9 24.5239C39.0419 24.8522 40.205 24.0751 40.3387 22.8944C40.5085 21.3946 42.2626 20.668 43.4432 21.6084C44.3726 22.3488 45.7445 22.0759 46.3198 21.0362Z"
        fill="#163BBF" fill-opacity="0.8"/>
    </g>
    <defs>
      <filter id="filter0_bddddi_1097_5755" x="13" y="21" width="61" height="97" filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_1097_5755" result="effect2_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_1097_5755" result="effect3_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_1097_5755" result="effect4_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_1097_5755" result="effect5_dropShadow_1097_5755"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1097_5755" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1097_5755"/>
      </filter>
      <filter id="filter1_bddddi_1097_5755" x="22" y="21" width="61" height="101" filterUnits="userSpaceOnUse"
              color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_1097_5755" result="effect2_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_1097_5755" result="effect3_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_1097_5755" result="effect4_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_1097_5755" result="effect5_dropShadow_1097_5755"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1097_5755" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1097_5755"/>
      </filter>
      <filter id="filter2_bddddi_1097_5755" x="-4.55457" y="-15.5546" width="105.109" height="115.109"
              filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_1097_5755" result="effect2_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_1097_5755" result="effect3_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_1097_5755" result="effect4_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_1097_5755" result="effect5_dropShadow_1097_5755"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1097_5755" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1097_5755"/>
      </filter>
      <filter id="filter3_bddddi_1097_5755" x="7.04572" y="-3.95427" width="81.9086" height="91.9085"
              filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
        <feGaussianBlur in="BackgroundImageFix" stdDeviation="12"/>
        <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="1.5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.08 0"/>
        <feBlend mode="normal" in2="effect1_backgroundBlur_1097_5755" result="effect2_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="6"/>
        <feGaussianBlur stdDeviation="3"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.07 0"/>
        <feBlend mode="normal" in2="effect2_dropShadow_1097_5755" result="effect3_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="14"/>
        <feGaussianBlur stdDeviation="4"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.04 0"/>
        <feBlend mode="normal" in2="effect3_dropShadow_1097_5755" result="effect4_dropShadow_1097_5755"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="24"/>
        <feGaussianBlur stdDeviation="5"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.01 0"/>
        <feBlend mode="normal" in2="effect4_dropShadow_1097_5755" result="effect5_dropShadow_1097_5755"/>
        <feBlend mode="normal" in="SourceGraphic" in2="effect5_dropShadow_1097_5755" result="shape"/>
        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
                       result="hardAlpha"/>
        <feOffset dy="2"/>
        <feGaussianBlur stdDeviation="2"/>
        <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
        <feColorMatrix type="matrix" values="0 0 0 0 0.0862745 0 0 0 0 0.231373 0 0 0 0 0.74902 0 0 0 0.12 0"/>
        <feBlend mode="normal" in2="shape" result="effect6_innerShadow_1097_5755"/>
      </filter>
    </defs>
  </svg>

}
