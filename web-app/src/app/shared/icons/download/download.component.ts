import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBaseComponent } from '@/app/shared/icons/icon-base/icon-base.component';

@Component({
  selector: 'fish-icon-download',
  standalone: true,
  imports: [],
  templateUrl: './download.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IconDownloadComponent extends IconBaseComponent<'32'> {

}
