import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IconBaseComponent } from '@/app/shared/icons/icon-base/icon-base.component';

@Component({
  selector: 'fish-icon-number',
  standalone: true,
  imports: [],
  templateUrl: './number.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IconNumberComponent extends IconBaseComponent<'32'> {
  /**
   * The number to display inside the SVG box
   */
  public number = input<number>(0);
}
