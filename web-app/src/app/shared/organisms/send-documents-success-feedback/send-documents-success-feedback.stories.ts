import { TranslateModule } from '@ngx-translate/core';
import { Meta, StoryObj, moduleMetadata } from '@storybook/angular';

import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconSendComponent } from '@/app/shared/icons/send/send.component';
import { SendDocumentsDialogComponent } from '@/app/shared/organisms/send-documents-dialog/send-documents-dialog.component';
import { SendDocumentsSuccessFeedbackComponent } from '@/app/shared/organisms/send-documents-success-feedback/send-documents-success-feedback.component';

const meta: Meta<SendDocumentsSuccessFeedbackComponent> = {
  title: 'SendDocumentsSuccessFeedback',
  component: SendDocumentsSuccessFeedbackComponent,
  decorators: [
    moduleMetadata({
      imports: [TranslateModule, ButtonComponent, IconSendComponent],
    }),
  ],
  render: () => ({
    template: `
    <fish-button (clicked)="SendDocumentsSuccessFeedback.open()" size="m" [class]="'w-36'">
      <fish-icon-send size="48" icon></fish-icon-send>
      <span [innerText]="'send_documents_dialog.button' | translate"></span>
    </fish-button>
    <fish-send-documents-success-feedback #SendDocumentsSuccessFeedback></fish-send-documents-success-feedback>
    `,
  }),
};

export default meta;

type Story = StoryObj<SendDocumentsDialogComponent>;

export const Default: Story = {};
