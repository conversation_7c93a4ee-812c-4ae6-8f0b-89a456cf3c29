import { TemplatePortal } from '@angular/cdk/portal';
import { ChangeDetectionStrategy, Component, Input, TemplateRef, ViewChild, input } from '@angular/core';

@Component({
  selector: 'fish-tab',
  imports: [],
  templateUrl: './tab.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TabComponent {
  public readonly disabled = input(false);

  public readonly labelButtonDataTestId = input<string>();

  public readonly label = input.required<string>();

  /** Allows grouping the Tabs, meaning inserting spaces between titles */
  @Input() public section?: string;

  @ViewChild(TemplateRef, { static: true })
  private readonly content!: TemplateRef<unknown>;

  public get templatePortal(): TemplatePortal {
    return new TemplatePortal(this.content, null!);
  }
}
