import { type Meta, type StoryObj, argsToTemplate, moduleMetadata } from '@storybook/angular';

import { IconCheckComponent } from '@/app/shared/icons/check/check.component';
import { IconLicenseCardComponent } from '@/app/shared/icons/license-card/license-card.component';

import { ButtonComponent } from './button.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<ButtonComponent> = {
  title: 'Button',
  component: ButtonComponent,
  argTypes: {
    size: {
      control: 'select',
      options: ['s', 'm', 'l'],
    },
    iconPosition: {
      control: 'select',
      options: ['left', 'right'],
    },
    type: {
      control: 'select',
      options: ['primary', 'secondary', 'warning'],
    },
  },
  decorators: [
    moduleMetadata({
      imports: [IconCheckComponent, IconLicenseCardComponent],
    }),
  ],
  render: (args) => ({
    props: {
      ...args,
    },
    template: `<fish-button ${argsToTemplate(args)}><fish-icon-license-card icon size="${
      args.size === 'l' ? 48 : 32
    }" />Fischereischein digitalisieren</fish-button>`,
  }),
};

export default meta;
type Story = StoryObj<ButtonComponent>;

export const Primary: Story = {
  args: {
    type: 'primary',
    size: 'm',
  },
};

export const Small: Story = {
  args: {
    type: 'primary',
    size: 's',
  },
};
export const Large: Story = {
  args: {
    type: 'primary',
    size: 'l',
  },
};

export const Secondary: Story = {
  args: {
    type: 'secondary',
  },
};

export const Warning: Story = {
  args: {
    type: 'warning',
  },
};

export const IconOnly: Story = {
  render: (args) => ({
    template: `<fish-button ${argsToTemplate(args)}><fish-icon-license-card icon size="32"/></fish-button>`,
  }),
};

export const Stretched: Story = {
  name: 'Stretched / Fixed width',
  render: (args) => ({
    template: `<fish-button ${argsToTemplate(args)} class="w-full"><fish-icon-license-card icon size="${args.size === 'l' ? 48 : 32}" />Fischereischein digitalisieren</fish-button>`,
  }),
};
