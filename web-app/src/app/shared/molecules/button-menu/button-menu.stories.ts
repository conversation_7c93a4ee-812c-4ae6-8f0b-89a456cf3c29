import { type Meta, type StoryObj, argsToTemplate } from '@storybook/angular';

import { ButtonMenuContentComponent } from '@/app/shared/atoms/button-menu-content/button-menu-content.component';
import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { IconEditComponent } from '@/app/shared/icons/edit/edit.component';
import { ButtonMenuComponent } from '@/app/shared/molecules/button-menu/button-menu.component';

// More on how to set up stories at: https://storybook.js.org/docs/writing-stories
const meta: Meta<ButtonMenuComponent> = {
  title: 'ButtonMenu',
  component: ButtonMenuComponent,
  args: {
    size: 'm',
    placeholder: 'Placeholder',
  },
  argTypes: {
    size: {
      control: 'radio',
      options: ['s', 'm', 'l'],
    },
  },
  render: (args) => ({
    props: args,
    template: `
    <fish-button-menu ${argsToTemplate(args)}>
        <fish-icon-edit size="32" open-icon></fish-icon-edit>

        <fish-button-menu-content>
            <fish-button size="m" type="secondary">Button 1</fish-button>
            <fish-button size="m" type="secondary">Button 2</fish-button>
            <fish-button size="m" type="secondary">Button 3</fish-button>
        </fish-button-menu-content>
    </fish-button-menu>
    `,
    moduleMetadata: {
      imports: [ButtonMenuContentComponent, ButtonComponent, IconEditComponent],
    },
  }),
};

export default meta;
type Story = StoryObj<ButtonMenuComponent>;

export const ButtonMenu: Story = {};
