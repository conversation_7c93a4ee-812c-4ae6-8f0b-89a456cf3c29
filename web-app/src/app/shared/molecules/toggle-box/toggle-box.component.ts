import { CdkPortalOutlet } from '@angular/cdk/portal';
import { CommonModule } from '@angular/common';
import {
  AfterContentInit,
  ChangeDetectionStrategy,
  Component,
  ContentChild,
  ContentChildren,
  EventEmitter,
  Input,
  Output,
  QueryList,
  ViewChild,
} from '@angular/core';

import { twMerge } from 'tailwind-merge';

import { ToggleBoxContentComponent } from '@/app/shared/atoms/toggle-box-content/toggle-box-content.component';
import { ToggleBoxTabComponent } from '@/app/shared/atoms/toggle-box-tab/toggle-box-tab.component';
import { SliderButtonGroupComponent } from '@/app/shared/molecules/slider-button-group/slider-button-group.component';
import { ToggleBoxHeaderComponent } from '@/app/shared/molecules/toggle-box-header/toggle-box-header.component';
import { toggleBoxVariants } from '@/app/shared/molecules/toggle-box/toggle-box.component.styles';

/**
 * The Form Box component height should be adjusted / given by the parent.
 */
@Component({
  selector: 'fish-toggle-box',
  imports: [CommonModule, ToggleBoxContentComponent, CdkPortalOutlet],
  templateUrl: './toggle-box.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ToggleBoxComponent implements AfterContentInit {
  @Input() public direction: 'horizontal' | 'vertical' = 'horizontal';

  /** Output that is sent once the page of the toggle box changes. As event data, the index of the new page is sent. */
  @Output() public readonly changed = new EventEmitter<number>();

  @ContentChildren(ToggleBoxTabComponent, { descendants: true })
  private readonly tabs!: QueryList<ToggleBoxTabComponent>;

  @ContentChild(SliderButtonGroupComponent, { descendants: true })
  private readonly sliderButtonGroup!: SliderButtonGroupComponent;

  @ContentChild(ToggleBoxHeaderComponent, { descendants: true })
  private readonly ToggleBoxHeader!: ToggleBoxHeaderComponent;

  @ViewChild(CdkPortalOutlet, { static: true })
  private readonly portalOutlet!: CdkPortalOutlet;

  protected get classes(): string {
    return twMerge(toggleBoxVariants({ direction: this.direction }));
  }

  public ngAfterContentInit(): void {
    if (this.tabs.length > 0) {
      this.setActiveTab(0);
    }

    this.sliderButtonGroup.changed.asObservable().subscribe((index) => {
      this.setActiveTab(index);
      this.changed.emit(index);
    });

    this.sliderButtonGroup.direction = this.direction;
    this.ToggleBoxHeader.direction = this.direction;
  }

  protected setActiveTab(index: number): void {
    const selectedTab = this.tabs.get(index)!;
    this.portalOutlet.detach();
    this.portalOutlet.attach(selectedTab.templatePortal);
  }
}
