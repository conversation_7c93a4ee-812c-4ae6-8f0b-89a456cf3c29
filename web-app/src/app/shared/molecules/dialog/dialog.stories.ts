import { Component, Input, ViewChild } from '@angular/core';

import { type Meta, StoryObj, argsToTemplate, moduleMetadata } from '@storybook/angular';

import { ButtonComponent } from '@/app/shared/atoms/button/button.component';
import { DialogContentComponent } from '@/app/shared/molecules/dialog-content/dialog-content.component';
import { DialogFooterComponent } from '@/app/shared/molecules/dialog-footer/dialog-footer.component';
import { DialogHeaderComponent } from '@/app/shared/molecules/dialog-header/dialog-header.component';
import { DialogComponent } from '@/app/shared/molecules/dialog/dialog.component';

@Component({
  selector: 'fish-story-wrapper',
  template: `
    <fish-button (clicked)="openDialog()">Open Dialog</fish-button>
    <fish-dialog #dialog>
      <fish-dialog-header>
        {{ headerText }}
      </fish-dialog-header>
      <fish-dialog-content>
        {{ contentText }}
      </fish-dialog-content>
      <fish-dialog-footer>
        <fish-button (clicked)="closeDialog()">
          {{ closeButtonText }}
        </fish-button>
      </fish-dialog-footer>
    </fish-dialog>
  `,
})
class StoryWrapperComponent {
  @Input()
  public headerText = 'Dialog Header';

  @Input()
  public contentText = 'This is the dialog content.';

  @Input()
  public closeButtonText = 'Close';

  @ViewChild('dialog') private readonly dialog!: DialogComponent;

  protected openDialog(): void {
    this.dialog.openDialog();
  }

  protected closeDialog(): void {
    this.dialog.closeDialog();
  }
}

const meta: Meta<StoryWrapperComponent> = {
  title: 'Dialog',
  component: StoryWrapperComponent,
  subcomponents: {
    Dialog: DialogComponent,
    DialogHeader: DialogHeaderComponent,
    DialogContent: DialogContentComponent,
    DialogFooter: DialogFooterComponent,
  },
  decorators: [
    moduleMetadata({
      declarations: [StoryWrapperComponent],
      imports: [DialogComponent, DialogHeaderComponent, DialogContentComponent, DialogFooterComponent, ButtonComponent],
    }),
  ],
  render: (args) => ({
    props: args,
    template: `
        <fish-dialog #dialog>
          <fish-dialog-header>
            ${args.headerText}
          </fish-dialog-header>
          <fish-dialog-content>
            ${args.contentText}
          </fish-dialog-content>
          <fish-dialog-footer>
            <fish-button (clicked)="closeDialog()">
              ${args.closeButtonText}
            </fish-button>
          </fish-dialog-footer>
        </fish-dialog>

        <!-- Wraps the opening and closing of the dialog component. -->
        <fish-story-wrapper ${argsToTemplate(args)}></fish-story-wrapper>`,
  }),
};

export default meta;

type Story = StoryObj<StoryWrapperComponent>;

export const Primary: Story = {
  args: {
    headerText: 'Dialog Header',
    contentText: 'This is the dialog content. You can put any text here.',
    closeButtonText: 'Close',
  },
};

export const LongContent: Story = {
  args: {
    headerText: 'Long Content Dialog',
    contentText:
      'Lorem ipsum dolor sit amet, consectetur adipiscing elit. Nullam euismod, nisl eget aliquam ultricies, nunc nisl aliquet nunc, vitae aliquam nisl nunc vitae nisl. '.repeat(
        5
      ),
    closeButtonText: 'OK',
  },
};
