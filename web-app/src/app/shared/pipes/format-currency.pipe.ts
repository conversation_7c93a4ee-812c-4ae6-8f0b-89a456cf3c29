import { formatNumber } from '@angular/common';
import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'formatCurrency',
  standalone: true,
})
export class FormatCurrencyPipe implements PipeTransform {
  public transform(value: number | null | undefined, digitsInfo: string = '1.2'): string {
    let formattedNumber: string;

    if (value === null || value === undefined) {
      formattedNumber = '—';
    } else {
      formattedNumber = formatNumber(value, 'de-DE', digitsInfo);
    }

    return `${formattedNumber} €`;
  }
}
