{"@@locale": "de", "@versionTextSemantic": {"placeholders": {"version": {}}, "type": "text"}, "alertDialogBiometricQuestion": "<PERSON><PERSON><PERSON><PERSON> Sie sich mit biometrischen Daten anmelden?", "@alertDialogBiometricQuestion": {}, "alertDialogBiometricReason": "Die Verwendung biometrischer Daten beschleunigt die Anmeldung.", "@alertDialogBiometricReason": {}, "alertDialogBiometricAnswerYes": "<PERSON><PERSON>, ich möchte den biometrischen Login aktivieren.", "@alertDialogBiometricAnswerYes": {}, "alertDialogBiometricAnswerNo": "<PERSON><PERSON>, ich möchte den biometrischen Login nicht aktivieren.", "@alertDialogBiometricAnswerNo": {}, "appTitle": "Kontroll-App", "@appTitle": {}, "appTitleSemantic": "Kontroll-<PERSON><PERSON>", "@appTitleSemantic": {}, "appName": "Digifischdok", "@appName": {}, "automaticLogoutText": "Logout in", "@automaticLogoutText": {}, "automaticLogoutTextSematic": "automatischer Logout in", "@automaticLogoutTextSematic": {}, "backToStartScreen": "Home", "@backToStartScreen": {}, "backButtonSemamticTitle": "Home Button", "@backButtonSemamticTitle": {}, "barcodeSematicText": "Option QR-Code scannen wählen", "@barcodeSematicText": {}, "controllOptionsTitle": "Kontrolloptionen", "@controllOptionsTitle": {}, "controllOptionsTitleSemantic": "Kontrolloptionen Titel", "@controllOptionsTitleSemantic": {}, "deviceInfoText": "\nGeräte Information: ", "@deviceInfoText": {}, "dialogErrorContentPlatformError": "Ein Platform Error ist aufgetreten. Bitte versuche es später erneut.", "@dialogErrorContentPlatformError": {}, "dialogErrorTitlePlatformError": "Platform Error", "@dialogErrorTitlePlatformError": {}, "dialogExternalWebPageText": "Sie verlassen nun die App und öffnen einen externen Link in Ihrem Browser.", "@dialogExternalWebPageText": {}, "dialogExternalWebPageTitle": "Externen Link", "@dialogExternalWebPageTitle": {}, "dialogOptionCancel": "Abbrechen", "@dialogOptionCancel": {}, "dialogOptionOk": "OK", "@dialogOptionOk": {}, "dialogOptionYes": "<PERSON>a", "@dialogOptionYes": {}, "dialogOptionNo": "<PERSON><PERSON>", "@dialogOptionNo": {}, "errorCodeText": "Fehlercode", "@errorCodeText": {}, "errorCodeContentText": "505", "@errorCodeContentText": {}, "formFieldClearButtonTooltip": "den Inhalt des Formularfelds löschen", "@formFieldClearButtonTooltip": {}, "formFieldObscureButtonTooltipHide": "den Inhalt ausblenden", "@formFieldObscureButtonTooltipHide": {}, "formFieldObscureButtonTooltipShow": "den Inhalt anzeigen", "@formFieldObscureButtonTooltipShow": {}, "infoTagText": "Halten Sie das Quadrat\nüber den QR-Code", "@infoTagText": {}, "infoAndroidSDKText": "\nAndroid SDK: ", "@infoAndroidSDKText": {}, "imprintSemantic": "Impressum öffnen", "@imprintSemantic": {}, "logoSemanticTitle": "Digifischdok Logo", "@logoSemanticTitle": {}, "logoutAnnounce": "Sie werden abgemeldet.", "@logoutAnnounce": {}, "menuAGB": "Nutzungsbedingungen", "@menuAGB": {}, "menuAGBSemantic": "AGBs öffnen", "@menuAGBSemantic": {}, "menuIcon": "<PERSON><PERSON>", "@menuIcon": {}, "menuIconOpened": "<PERSON><PERSON> sch<PERSON>ßen", "@menuIconOpened": {}, "menuImprint": "Impressum", "@menuImprint": {}, "menuPrivacy": "Datenschutz", "@menuPrivacy": {}, "menuSignout": "Abmelden", "@menuSignout": {}, "menuSupport": "Support", "@menuSupport": {}, "minutesText": "Min", "@minutesText": {}, "menuSupportSemantic": "Support öffnen", "@menuSupportSemantic": {}, "menuPopUpSemanticText": "Menu Pop Up öffnen", "@menuPopUpSemanticText": {}, "networkInfoTitle": "<PERSON><PERSON><PERSON><PERSON>", "@networkInfoTitle": {}, "networkInfoErrorTag": "<PERSON>ine <PERSON>bindung", "@networkInfoErrorTag": {}, "networkInfoHint": "Überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut.", "@networkInfoHint": {}, "nfcScanSematicText": "Option NFC scannen wählen", "@nfcScanSematicText": {}, "nfcSnackBarNotLoggedIn": "Bitte melden Sie sich an.", "@nfcSnackBarNotLoggedIn": {}, "nfcSnackBarLoggedIn": "Bitte wählen Sie Fischereischein scannen.", "@nfcSnackBarLoggedIn": {}, "nfcRegisterPageTitle": "NFC scannen", "@nfcRegisterPageTitle": {}, "nfcTagInfoTitle": "Halten Sie Ihr Telefon an die Fischereischeinscheckkarte", "@nfcTagInfoTitle": {}, "nfcNotReadTitle": "Konnte NFC Chip nicht lesen", "@nfcNotReadTitle": {}, "nfcNotReadText": "Die Karte konnte leider nicht gelesen werden. Wiederholen Sie den Scan und halten Sie die Karte an verschiedene\nStellen Ihres Telefons.", "@nfcNotReadText": {}, "nfcNotReadTextSemantic": "Die Karte konnte leider nicht gelesen werden. Wiederholen Sie den Scan und halten Sie die Karte an verschiedene Stellen Ihres Telefons.", "@nfcNotReadTextSemantic": {}, "nfcReaderDisbledText": "NFC Reader ist deaktiviert", "@nfcReaderDisbledText": {}, "nfcReaderDisabledInfoText": "Gehen Sie in die Einstellungen Ihres Smartphones und aktivieren Sie bitte die NFC-Funktion.", "@nfcReaderDisabledInfoText": {}, "notConnectedText": "Überprüfen Sie Ihre Internetverbindung und versuchen Sie es erneut.", "@notConnectedText": {}, "menuPrivacySematic": "Datenschutz öffnen", "@menuPrivacySematic": {}, "readNfcTitle": "<PERSON><PERSON><PERSON><PERSON> scan<PERSON>", "@readNfcTitle": {}, "fishingLicense": "<PERSON><PERSON><PERSON><PERSON>", "@fishingLicense": {}, "fishingTax": "Fischereiabgabe", "@fishingTax": {}, "resultPageAnnounce": "<PERSON><PERSON><PERSON>, <PERSON><PERSON>", "@resultPageAnnounce": {}, "resultPageSummaryHeaderApproved": "<PERSON><PERSON><PERSON><PERSON>", "@resultPageSummaryHeaderApproved": {}, "resultPageSummaryHeaderUnclear": "Unklar", "@resultPageSummaryHeaderUnclear": {}, "resultPageSummaryHeaderBlocked": "Fischereischein gesperrt", "@resultPageSummaryHeaderBlocked": {}, "resultPageSummaryHeaderFishingLicenseMissing": "Fischereischein- \npflicht prüfen", "@resultPageSummaryHeaderFishingLicenseMissing": {}, "resultPageSummaryHeaderFishingTaxMissing": "Fischerei- \nabgabe fehlt", "@resultPageSummaryHeaderFishingTaxMissing": {}, "positive": "positiv", "@positive": {}, "negative": "negativ", "@negative": {}, "resultPageTestedPerson": "Geprüfte Person", "@resultPageTestedPerson": {}, "resultPageTestedParameter": "Geprüfte Parameter", "@resultPageTestedParameter": {}, "resultPageNoticeTypeSufficient": "Fischereiabgabe ist ausreichend", "@resultPageNoticeTypeSufficient": {}, "resultPageNoticeTypeBlocked": "Fischereischein ist gesperrt", "@resultPageNoticeTypeBlocked": {}, "resultPageNoticeTypeNotRequired": "Wird ggf. nicht benö<PERSON>gt", "@resultPageNoticeTypeNotRequired": {}, "resultPageNoResultTag": "<PERSON><PERSON> gefunden", "@resultPageNoResultTag": {}, "resultPageNoInternetTag": "<PERSON>ine <PERSON>bindung", "@resultPageNoInternetTag": {}, "registerSearchBirthdateFieldHint": "Geburtsdatum", "@registerSearchBirthdateFieldHint": {}, "registerSearchFirstnameFieldHint": "<PERSON><PERSON><PERSON>", "@registerSearchFirstnameFieldHint": {}, "registerSearchID": "ID", "@registerSearchID": {}, "registerSearchLastnameFieldHint": "Nachname", "@registerSearchLastnameFieldHint": {}, "reportContactDataUrl": "mailto:<EMAIL>?subject=Fehlermeldung DigiFischDok-App&body=", "@reportContactDataUrl": {}, "resultPageTitle": "Kontrollergebnis", "@resultPageTitle": {}, "rootNavigationPageItemBarcodeTitle": "QR-Code scannen", "@rootNavigationPageItemBarcodeTitle": {}, "scannerActive": "Scanner aktiv", "@scannerActive": {}, "scanRepeatTitle": "<PERSON><PERSON> wied<PERSON>n", "@scanRepeatTitle": {}, "scanFailSematicText": "Scan fehlgeschlagen wiederholen wählen", "@scanFailSematicText": {}, "settingsPageAppearanceBasis": "Standard", "@settingsPageAppearanceBasis": {}, "settingsPageLanguageSystem": "System", "@settingsPageLanguageSystem": {}, "settingsPageSignOutButtonTitle": "Abmelden", "@settingsPageSignOutButtonTitle": {}, "signInPageHintTextPassword": "Passwort", "@signInPageHintTextPassword": {}, "signInPageHintTextPasswordSematic": "Bitte das Passwort eingeben", "@signInPageHintTextPasswordSematic": {}, "signInPageLabelTextUsername": "<PERSON><PERSON><PERSON><PERSON>", "@signInPageLabelTextUsername": {}, "signInPageLabelTextUsernameSemantic": "Bitte den Mitarbeiter Benutzername eingeben", "@signInPageLabelTextUsernameSemantic": {}, "signInPageHintTextUsername": "E-Mail Adresse", "@signInPageHintTextUsername": {}, "signInPageLoginInvite": "Bitte loggen Sie sich ein", "@signInPageLoginInvite": {}, "signInPageLoginInviteSemanticTitle": "Bitte loggen Sie sich ein Untertitel", "@signInPageLoginInviteSemanticTitle": {}, "signInPageSignInButtonTitle": "<PERSON><PERSON>", "@signInPageSignInButtonTitle": {}, "signInPageSignInError": "Autorisierung fehlgeschlagen", "@signInPageSignInError": {}, "signInPageValidatorInvalidMessagePassword": "<PERSON><PERSON>en Si<PERSON> ein Passwort ein", "@signInPageValidatorInvalidMessagePassword": {}, "signInPageValidatorInvalidMessageUsername": "<PERSON><PERSON><PERSON> ein <PERSON>utzername ein", "@signInPageValidatorInvalidMessageUsername": {}, "signInPageValidateAnnounce": "<PERSON><PERSON><PERSON> ein <PERSON>utzername ein", "@signInPageValidateAnnounce": {}, "signInPageValidateFailAnnounce": "Es liegt ein oder mehrere Fehler in der Form: ", "@signInPageValidateFailAnnounce": {}, "signInPageVersion": "Version", "@signInPageVersion": {}, "snackBarErrorContent": "Verdammt! Ein Flutter Error!", "@snackBarErrorContent": {}, "startScreenAnnounce": "Login war erfolgreich", "@startScreenAnnounce": {}, "startScanText": "<PERSON><PERSON>", "@startScanText": {}, "supportGreetingText": "G<PERSON>n Tag,\n ich möchte folgende Meldung bzgl. der DigiFischDok-App machen:\n", "@supportGreetingText": {}, "supportDescriptionText": "\n<PERSON>ne Fehlerbeschreibung:\n", "@supportDescriptionText": {}, "supportFormalClosing": "\nMit freundlichen Grüßen", "@supportFormalClosing": {}, "versionText": "\nApp-Version: ", "@versionText": {}, "versionTextSemantic": "App Version {version} Titel", "welcomePageAnnounce": "Nutzer ist ausgeloggt", "@welcomePageAnnounce": {}}