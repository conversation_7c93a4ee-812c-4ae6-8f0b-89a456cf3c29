import 'package:app_mobile/app/pages/widgets/error_code_table.dart';
import 'package:app_mobile/features/automatic_logout/widgets/automatic_logout.dart';
import 'package:app_mobile/features/nfc_scanner/widgets/nfc_not_activated_content.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:flutter_test/flutter_test.dart';

import '../../../utils/test_utils.dart';

void main() {
  group('NfcNotActivatedContent Widget Tests', () {
    testWidgets('renders NfcNotActivatedContent components',
        (WidgetTester tester) async {
      await tester.pumpWidget(
        buildTestWidget(
          child: const NfcNotActivatedContent(
            width: 0,
          ),
          overrides: [],
        ),
      );

      expect(find.text('NFC Reader ist deaktiviert'), findsOneWidget);
      expect(
        find.text(
          'Geh<PERSON> Sie in die Einstellungen Ihres Smartphones und aktivieren Sie bitte die NFC-Funktion.',
        ),
        findsOneWidget,
      );
      expect(find.byType(ErrorCodeTable), findsOneWidget);
      expect(find.text('Starte Scan'), findsOneWidget);
      expect(find.byType(SvgPicture), findsOneWidget);
      expect(find.byType(AutomaticLogoutWidget), findsOneWidget);
    });

    testWidgets('ScrollController exists', (WidgetTester tester) async {
      await tester.pumpWidget(
        buildTestWidget(
          child: const NfcNotActivatedContent(
            width: 0,
          ),
          overrides: [],
        ),
      );

      final scrollableFinder = find.byType(Scrollable);
      expect(scrollableFinder, findsOneWidget);

      await tester.drag(scrollableFinder, const Offset(0, -300));
      await tester.pumpAndSettle();
    });

    testWidgets('DisabledButton is disabled', (WidgetTester tester) async {
      await tester.pumpWidget(
        buildTestWidget(
          child: const NfcNotActivatedContent(
            width: 0,
          ),
          overrides: [],
        ),
      );
      final disabledButton = find.text('Starte Scan');
      expect(disabledButton, findsOneWidget);

      await tester.tap(disabledButton);
      await tester.pump();
    });
  });
}
