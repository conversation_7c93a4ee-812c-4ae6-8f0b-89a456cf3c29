import 'package:app_mobile/features/barcode_scanner/state/scan_state_enum.dart';
import 'package:app_mobile/features/barcode_scanner/state/scanner_control_state.dart';
import 'package:app_mobile/features/central_search/domain/entities/license_status_enum.dart';
import 'package:app_mobile/features/central_search/domain/entities/search_parameters.dart';
import 'package:app_mobile/features/central_search/domain/entities/search_result.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  group('ScannerControlState', () {
    test('should have null default values', () {
      const state = ScannerControlState();

      expect(state.searchParameters, isNull);
      expect(state.searchResults, isNull);
      expect(state.scanState, isNull);
      expect(state.error, isNull);
    });

    test('should create a state with provided values', () {
      final searchParams = SearchParameters(
        identificationDocumentId: '1',
        hash: 'abc',
      );
      final searchResults = SearchResult(
        licenseNumber: 'SH12151515151515',
        licenseType: 'regular',
        licenseStatus: LicenseStatus.valid,
        licenseNote: '',
        taxValid: true,
        taxNote: '',
        givenNames: 'Test givenName',
        surname: 'Test Surname',
        birthdate: '1.1.2001',
      );
      const scanState = ScanStateEnum.idle;
      final error = Exception('An error occurred');

      final state = ScannerControlState(
        searchParameters: searchParams,
        searchResults: searchResults,
        scanState: scanState,
        error: error,
      );

      expect(state.searchParameters, equals(searchParams));
      expect(state.searchResults, equals(searchResults));
      expect(state.scanState, equals(scanState));
      expect(state.error, equals(error));
    });

    test('should support copyWith method', () {
      final initialState = ScannerControlState(
        searchParameters: SearchParameters(
          identificationDocumentId: '1',
          hash: 'abc',
        ),
        searchResults: SearchResult(
          licenseNumber: 'SH12151515151515',
          licenseType: 'regular',
          licenseStatus: LicenseStatus.valid,
          licenseNote: '',
          taxValid: true,
          taxNote: '',
          givenNames: 'Test givenName',
          surname: 'Test Surname',
          birthdate: '1.1.2001',
        ),
        scanState: ScanStateEnum.idle,
        error: Exception('An error occurred'),
      );

      final newSearchParams = SearchParameters(
        identificationDocumentId: '1',
        hash: 'abc',
      );
      final newState = initialState.copyWith(
        searchParameters: newSearchParams,
        scanState: ScanStateEnum.success,
        error: null,
      );

      expect(newState.searchParameters, equals(newSearchParams));
      expect(newState.searchResults, equals(initialState.searchResults));
      expect(newState.scanState, equals(ScanStateEnum.success));
      expect(newState.error, isNull);
    });
  });
}
