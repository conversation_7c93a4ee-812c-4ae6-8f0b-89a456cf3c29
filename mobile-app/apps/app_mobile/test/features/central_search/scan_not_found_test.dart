import 'package:app_mobile/app/pages/widgets/error_code_table.dart';
import 'package:app_mobile/features/central_search/widgets/scan_not_found.dart';
import 'package:design/design.dart';

import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../utils/test_utils.dart';

void main() {
  testWidgets('ScanNotFound widget test', (WidgetTester tester) async {
    final container = ProviderContainer();
    addTearDown(container.dispose);

    await tester.pumpWidget(
      buildTestWidget(
        child: const ScanNotFound(),
        overrides: [],
      ),
    );

    expect(find.byType(ErrorTag), findsOneWidget);
    expect(find.byType(ErrorCodeTable), findsOneWidget);
  });
}
