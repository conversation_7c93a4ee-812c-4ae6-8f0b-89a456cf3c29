import 'package:app_mobile/features/imprint/domain/imprint_use_case.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  TestWidgetsFlutterBinding.ensureInitialized();

  group('ImprintUseCase', () {
    late ImprintUseCase imprintUseCase;

    setUp(() {
      imprintUseCase = ImprintUseCase();
    });

    test('should return the content of imprint.html', () async {
      const MethodChannel channel = MethodChannel('flutter/assets');
      const String mockHtmlContent = '<html>Sample imprint Content</html>';

      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMessageHandler(channel.name, (ByteData? message) async {
        return ByteData.sublistView(
          Uint8List.fromList(mockHtmlContent.codeUnits),
        );
      });
      final result = await imprintUseCase.call();

      expect(result, mockHtmlContent);
    });

    tearDown(() {
      TestDefaultBinaryMessengerBinding.instance.defaultBinaryMessenger
          .setMockMessageHandler('flutter/assets', null);
    });
  });
}
