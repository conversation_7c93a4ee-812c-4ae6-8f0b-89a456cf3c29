import 'package:app_mobile/features/auth/version_number.dart';
import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../utils/test_utils.dart';

void main() {
  TestWidgetsFlutterBinding
      .ensureInitialized(); // Ensure test binding is initialized

  setUp(() {
    PackageInfo.setMockInitialValues(
      appName: 'abc',
      packageName: 'com.example.example',
      version: '1.0.0',
      buildNumber: '2',
      buildSignature: 'buildSignature',
    );
  });

  testWidgets('displays version number when future is complete',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      buildTestWidget(
        child: const Scaffold(
          body: VersionNumber(),
        ),
        overrides: [],
      ),
    );

    // Verify that the FutureBuilder is in the waiting state
    expect(find.byType(SizedBox), findsOneWidget);

    // Let the Future complete
    await tester.pumpAndSettle();

    // Verify that the version number is displayed
    expect(find.textContaining('Version 1.0.0'), findsOneWidget);
  });

  testWidgets('displays SizedBox when future is not complete',
      (WidgetTester tester) async {
    await tester.pumpWidget(
      buildTestWidget(
        child: const Scaffold(
          body: VersionNumber(),
        ),
        overrides: [],
      ),
    );

    expect(find.byType(SizedBox), findsOneWidget);
  });
}
