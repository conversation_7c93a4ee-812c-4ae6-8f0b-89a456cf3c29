import 'package:app_mobile/dependency_injection/use_cases/agb_use_case.dart';
import 'package:app_mobile/features/agb/domain/agb_use_case.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_test/flutter_test.dart';

void main() {
  late ProviderContainer container;

  setUp(() {
    container = ProviderContainer();
  });

  tearDown(() {
    container.dispose();
  });

  test('agbUseCase should return an instance of AgbUseCase', () {
    final agbUseCaseInstance = container.read(agbUseCaseProvider);

    expect(agbUseCaseInstance, isA<AgbUseCase>());
  });
}
