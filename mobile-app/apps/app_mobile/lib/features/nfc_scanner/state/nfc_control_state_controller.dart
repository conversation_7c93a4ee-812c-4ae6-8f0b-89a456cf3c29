import 'dart:async';
import 'dart:io';

import 'package:riverpod_annotation/riverpod_annotation.dart';

import '../../../dependency_injection/use_cases/nfc_fisher_use_case.dart';
import '../../auth/domain/errors/token_extension_error.dart';
import '../../central_search/domain/entities/search_parameters.dart';
import 'nfc_control_state.dart';
import 'nfc_state_enum.dart';

part 'nfc_control_state_controller.g.dart';

@Riverpod(keepAlive: true)
class NfcStateController extends _$NfcStateController {
  @override
  FutureOr<NfcControlState> build() async {
    const state = NfcControlState(
      nfcSearchParameters: null,
      nfcResults: null,
      nfcState: NfcStateEnum.idle,
    );
    return state;
  }

  Future<void> notCompatible() async {
    state = const AsyncValue.loading();

    state = await AsyncValue.guard(() async {
      return state.requireValue.copyWith(
        nfcSearchParameters: null,
        nfcResults: null,
        nfcState: NfcStateEnum.notActivate,
      );
    });
  }

  Future<void> readNfcTag(SearchParameters searchParameters) async {
    try {
      state = const AsyncValue.loading();

      final useCase = ref.read(fisherUseCaseProvider);
      final results = await useCase.call(
        searchParameters,
      );
      state = AsyncValue.data(
        NfcControlState(
          nfcSearchParameters: searchParameters,
          nfcResults: results,
          nfcState: NfcStateEnum.success,
        ),
      );
    } catch (e) {
      if (e is SocketException) {
        state = AsyncValue.data(
          NfcControlState(
            nfcSearchParameters: searchParameters,
            nfcResults: null,
            nfcState: NfcStateEnum.scanNotConnected,
            error: e,
          ),
        );
      } else if (e is TokenExtensionError) {
        state = AsyncValue.data(
          NfcControlState(
            nfcSearchParameters: searchParameters,
            nfcResults: null,
            nfcState: NfcStateEnum.userIsNotAuthorized,
            error: e,
          ),
        );
      } else {
        state = AsyncValue.data(
          NfcControlState(
            nfcSearchParameters: searchParameters,
            nfcResults: null,
            nfcState: NfcStateEnum.notFound,
            error: e,
          ),
        );
      }
    }
  }

  Future<void> abortedSearch() async {
    state = const AsyncValue.loading();
    await Future.delayed(const Duration(seconds: 4));
    state = await AsyncValue.guard(() async {
      return state.requireValue.copyWith(
        nfcSearchParameters: null,
        nfcResults: null,
        nfcState: NfcStateEnum.fail,
      );
    });
  }

  Future<void> resetSearch() async {
    if (state.value != null && state.value!.nfcState != NfcStateEnum.idle) {
      state = await AsyncValue.guard(() async {
        return state.requireValue.copyWith(
          nfcSearchParameters: null,
          nfcResults: null,
          nfcState: NfcStateEnum.idle,
        );
      });
    }
  }
}
