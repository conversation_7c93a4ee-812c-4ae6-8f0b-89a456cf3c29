import 'package:design/design.dart';
import 'package:flutter/material.dart';

import '../../../app/app_index.dart';

class BiometricsDialogContent extends StatelessWidget {
  const BiometricsDialogContent(this.dialogContext, {super.key});
  final BuildContext dialogContext;

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(context.strings.alertDialogBiometricQuestion),
      content: Text(context.strings.alertDialogBiometricReason),
      actions: <Widget>[
        Row(
          mainAxisSize: MainAxisSize.max,
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: <Widget>[
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.30,
              child: PrimaryButton(
                semanticText: context.strings.alertDialogBiometricAnswerYes,
                title: context.strings.dialogOptionYes,
                onPressed: () => Navigator.pop(dialogContext, true),
                textColor: StyleGuideColors.brandSecondary,
              ),
            ),
            SizedBox(
              width: MediaQuery.of(context).size.width * 0.30,
              child: SecondaryButton(
                semanticText: context.strings.alertDialogBiometricAnswerNo,
                title: context.strings.dialogOptionNo,
                onPressed: () => Navigator.pop(dialogContext, false),
              ),
            ),
          ],
        ),
      ],
    );
  }
}
