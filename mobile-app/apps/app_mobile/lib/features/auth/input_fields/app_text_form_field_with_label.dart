import 'package:design/design.dart';
import 'package:flutter/material.dart';

import 'app_text_form_field.dart';

class AppTextFormFieldWithLabel extends StatefulWidget {
  const AppTextFormFieldWithLabel({
    super.key,
    this.autocorrect = true,
    this.autofillHints,
    this.controller,
    this.focusNode,
    this.hintSuffixText,
    this.hintText,
    this.initialValue,
    this.label,
    this.keyboardType,
    this.obscureText = false,
    this.onEditingComplete,
    this.onFieldSubmitted,
    this.onSaved,
    this.suffixIcon,
    this.textInputAction,
    this.validator,
    required this.semanticLabel,
  });

  final TextEditingController? controller;
  final FocusNode? focusNode;
  final Function(String?)? onSaved;
  final Function(String)? onFieldSubmitted;
  final Function()? onEditingComplete;
  final TextInputAction? textInputAction;
  final String? hintText;
  final String? hintSuffixText;
  final String? initialValue;
  final String? label;
  final Widget? suffixIcon;
  final String semanticLabel;
  final TextInputType? keyboardType;
  final Iterable<String>? autofillHints;
  final bool autocorrect;
  final String? Function(String?)? validator;
  final bool obscureText;

  @override
  State<AppTextFormFieldWithLabel> createState() =>
      _AppTextFormFieldWithLabelState();
}

class _AppTextFormFieldWithLabelState extends State<AppTextFormFieldWithLabel> {
  String? label;
  @override
  void initState() {
    super.initState();
    if (widget.label != null) {
      label = widget.label;
    } else {
      if (widget.label == null && widget.hintText != null) {
        label = widget.hintText;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (label != null)
          Text.rich(
            TextSpan(
              text: label!,
              style: context.theme.formLabel,
              children: <TextSpan>[
                if (widget.hintSuffixText != null)
                  TextSpan(
                    text: widget.hintSuffixText!,
                    style: const TextStyle(fontWeight: FontWeight.normal),
                  ),
              ],
            ),
          ),
        spacingVertical8,
        AppTextFormField(
          semanticLabel: widget.semanticLabel,
          focusNode: widget.focusNode,
          onSaved: widget.onSaved,
          onFieldSubmitted: widget.onFieldSubmitted,
          onEditingComplete: widget.onEditingComplete,
          textInputAction: widget.textInputAction,
          hintText: widget.hintText,
          suffixIcon: widget.suffixIcon,
          keyboardType: widget.keyboardType,
          autofillHints: widget.autofillHints,
          autocorrect: widget.autocorrect,
          validator: widget.validator,
          obscureText: widget.obscureText,
          initialValue: widget.initialValue,
        ),
      ],
    );
  }
}
