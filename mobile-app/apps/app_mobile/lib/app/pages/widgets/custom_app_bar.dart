import 'dart:ui';

import 'package:auto_size_text/auto_size_text.dart';
import 'package:design/design.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../app_index.dart';
import '../semantic_wrapper.dart';
import 'constants.dart';
import 'menu_icon/menu_icon_button.dart';

class CustomAppBar extends StatefulWidget implements PreferredSizeWidget {
  const CustomAppBar({
    super.key,
    this.title,
    required this.backButtonTitle,
    required this.extendedBehindAppBarBody,
    this.onPressed,
  });

  final String backButtonTitle;
  final bool extendedBehindAppBarBody;
  final String? title;
  final VoidCallback? onPressed;

  @override
  State<CustomAppBar> createState() => _CustomAppBarState();

  @override
  Size get preferredSize {
    final double height =
        title != null ? kToolbarWithTitleHeight : kToolbarHeight;
    return Size.fromHeight(height);
  }
}

class _CustomAppBarState extends State<CustomAppBar> {
  @override
  Widget build(BuildContext context) {
    bool isMenuOpen = false;

    void toggleMenuState() {
      setState(() {
        isMenuOpen = !isMenuOpen;
        SemanticWrapper.menuOpenValue.value =
            !SemanticWrapper.menuOpenValue.value;
      });
    }

    final size = MediaQuery.of(context).size;
    const double distance = 0.0135;
    return widget.title != null
        ? ClipRRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                color:
                    widget.extendedBehindAppBarBody ? Colors.transparent : null,
                decoration: !widget.extendedBehindAppBarBody
                    ? BoxDecoration(
                        gradient: context
                            .theme.appColorsTheme.appBarBackgroundGradient,
                      )
                    : null,
                child: SingleChildScrollView(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      spacingVertical35,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SemanticWrapper(
                            child: GestureDetector(
                              onTap: widget.onPressed,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Padding(
                                    padding: paddingTop6 +
                                        paddingBottom4 +
                                        paddingLeft24 +
                                        paddingRight8 +
                                        EdgeInsets.symmetric(
                                          vertical: size.height * distance,
                                        ),
                                    child: SizedBox.square(
                                      child: SvgPicture.asset(
                                        'assets/images/icons/return_back.svg',
                                        height: 16,
                                        width: 16,
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: paddingTop6 +
                                        paddingBottom4 +
                                        EdgeInsets.symmetric(
                                          vertical: size.height * distance,
                                        ),
                                    child: Text(
                                      semanticsLabel: context
                                          .strings.backButtonSemamticTitle,
                                      widget.backButtonTitle,
                                      style: context.theme.backButtonText,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Padding(
                            padding: EdgeInsets.symmetric(
                                  vertical: size.height * distance,
                                ) +
                                paddingRight24,
                            child: MenuIconButton(
                              onMenuToggle: () => toggleMenuState(),
                              menuMaxWidth: size.width,
                            ),
                          ),
                        ],
                      ),
                      SemanticWrapper(
                        child: Padding(
                          padding: widget.title ==
                                  context.strings
                                      .rootNavigationPageItemBarcodeTitle
                              ? const EdgeInsets.only(bottom: 18.0)
                              : EdgeInsets.zero,
                          child: FittedBox(
                            fit: BoxFit.scaleDown,
                            child: Semantics(
                              label: widget.title,
                              header: true,
                              child: AutoSizeText(
                                maxFontSize: widget.title ==
                                        context.strings.resultPageTitle
                                    ? 24
                                    : 28,
                                minFontSize: widget.title ==
                                        context.strings.resultPageTitle
                                    ? 24
                                    : 28,
                                widget.title!,
                                textAlign: TextAlign.center,
                                style: context.theme.header,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          )
        : Container(
            color: context.theme.appColorsTheme.appBarBackground,
            child: AppBar(
              backgroundColor: Colors.transparent,
              actions: [
                Padding(
                  padding: paddingRight16,
                  child: Semantics(
                    explicitChildNodes: true,
                    child: MenuIconButton(
                      menuMaxWidth: size.width,
                      onMenuToggle: () => toggleMenuState(),
                    ),
                  ),
                ),
              ],
            ),
          );
  }
}
