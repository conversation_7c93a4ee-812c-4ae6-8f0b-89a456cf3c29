## <PERSON><PERSON>pp

## Getting Started

### Architecture


| Application | State Management                                      |
|-------------|-------------------------------------------------------|
| app_mobile  | [riverpod](https://pub.dev/packages/riverpod)         |

### Structure

The project has a mono repo structure and uses [melos](https://melos.invertase.dev/) to maintain multiple packages in the same workspace.

### Tooling

| Feature                    | Tool                                                                    |
|----------------------------|-------------------------------------------------------------------------|
| Flutter Version Management | [fvm](https://pub.dev/packages/fvm)                                     |
| Mono Repo Management       | [melos](https://pub.dev/packages/melos)                                 |
| Code Generation            | [build_runner](https://pub.dev/packages/build_runner)                   |
| Barrel Files               | [index_generator](https://pub.dev/packages/index_generator)             |
| Localization               | [intl](https://pub.dev/packages/intl)                                   |
| Native Splash Screen       | [flutter_native_splash](https://pub.dev/packages/flutter_native_splash) |

### Installation

Install melos

```sh
dart pub global activate melos
```

Install fvm & configure it

```sh
dart pub global activate fvm

fvm use --force
```

Install tools

```sh
melos toolchain
```

Run melos

```sh
melos bootstrap
``` 
or 
```sh
melos bs
```

Run build_runner in every app / package / feature

```sh
melos build
```

Run index_generator in every app / package / feature

```sh
melos index
```

Run gen-l10n in every app / package / feature for generating localization files

```sh
melos l10n
```


## Build Environment

### Development

```sh
flutter run --dart-define="ENV=dev"
```

### Staging

```sh
flutter run --dart-define="ENV=stage"
```

### Production

```sh
flutter run --dart-define="ENV=prod"
```

## Flutter Tools


### Analyze

```sh
dart run flutter_tools alias run --alias analyze
```

### Test

#### Unit Test

```sh
dart run flutter_tools alias run --alias test
```

#### Integration Test Android

```sh
dart run flutter_tools alias run --alias integration_test_android
```

#### Integration Test iOS

```sh
dart run flutter_tools alias run --alias integration_test_ios
```

### Build

#### Android 

```sh
dart run flutter_tools alias run --alias build_android --configuration internal_release
dart run flutter_tools alias run --alias build_android --configuration preview_release
dart run flutter_tools alias run --alias build_android --configuration release_candidate
dart run flutter_tools alias run --alias build_android --configuration store_release
```

#### iOS 

```sh
dart run flutter_tools alias run --alias build_ios --configuration internal_release
dart run flutter_tools alias run --alias build_ios --configuration preview_release
dart run flutter_tools alias run --alias build_ios --configuration release_candidate
dart run flutter_tools alias run --alias build_ios --configuration store_release
```

### Distribution

#### App Center

```sh
dart run flutter_tools alias run --alias distribute_app_center
```

#### App Store Connect

```sh
dart run flutter_tools alias run --alias distribute_ios
```
