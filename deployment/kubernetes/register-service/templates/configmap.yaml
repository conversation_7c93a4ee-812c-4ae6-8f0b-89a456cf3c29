apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "register-service.fullname" . }}
  labels:
    {{- include "register-service.labels" . | nindent 4 }}
data:
  {{- toYaml .Values.properties | nindent 4 }}
  {{- /*  @formatter:off*/}}
    DATABASE_USERNAME: {{ .Values.externalDatabase.user | quote }}
    DATABASE_PASSWORD: {{ .Values.externalDatabase.password | quote }}
    DATABASE_URL: jdbc:postgresql://{{ .Values.externalDatabase.host }}:{{ .Values.externalDatabase.port }}/{{ .Values.externalDatabase.databaseName }}?zeroDateTimeBehavior=convertToNull&tcpKeepAlive=true
    KEYCLOAK_ISSUER_URI: {{ .Values.externalKeycloak.issuerUri | quote }}
    KEYCLOAK_CLIENT_ID: {{ .Values.externalKeycloak.clientId | quote }}
    KEYCLOAK_CLIENT_SECRET: {{ .Values.externalKeycloak.clientSecret | quote }}
    SMTP_HOST: {{ .Values.externalMail.host | quote }}
    SMTP_PORT: {{ .Values.externalMail.port | quote }}
    SMTP_USERNAME: {{ .Values.externalMail.user | quote }}
    SMTP_PASSWORD: {{ .Values.externalMail.password | quote }}
    SMTP_FROM: {{ .Values.externalMail.from | quote }}
    OS_INBOX_TOKEN_URI: {{ .Values.osInbox.tokenUri | quote }}
    OS_INBOX_CLIENT_ID: {{ .Values.osInbox.clientId | quote }}
    OS_INBOX_CLIENT_SECRET: {{ .Values.osInbox.clientSecret | quote }}
    OS_INBOX_CLIENT_SCOPE: {{ .Values.osInbox.clientScope | quote }}
    OS_INBOX_RESOURCE: {{ .Values.osInbox.resource | quote }}
    OS_INBOX_BASE_URN: {{ .Values.osInbox.baseUrn | quote }}
    S3_ENDPOINT: {{ .Values.s3.endpoint | quote }}
    S3_BUCKET: {{ .Values.s3.bucket | quote }}
    S3_REGION: {{ .Values.s3.region | quote }}
    S3_ACCESS_KEY: {{ .Values.s3.accessKey | quote }}
    S3_SECRET_KEY: {{ .Values.s3.secretKey | quote }}
    CARD_ORDER_SERVICE_TOKEN_URI: {{ .Values.cardOrder.tokenUri | quote }}
    CARD_ORDER_SERVICE_CLIENT_ID: {{ .Values.cardOrder.clientId | quote }}
    CARD_ORDER_SERVICE_CLIENT_SECRET: {{ .Values.cardOrder.clientSecret | quote }}
    CARD_ORDER_SERVICE_TEMPLATE_VERSION: {{ .Values.cardOrder.templateVersion | quote }}
    CARD_ORDER_SERVICE_BASE_URL: {{ .Values.cardOrder.baseUrl | quote }}
    CARD_ORDER_SERVICE_ENDPOINT_PATH: {{ .Values.cardOrder.endpointPath | quote }}
    CARD_ORDER_SERVICE_DAYS_UNTIL_DELETION: {{ .Values.cardOrder.daysUntilDeletion | quote }}
{{- /*  @formatter:on*/}}