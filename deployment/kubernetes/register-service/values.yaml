# Default values for register-service.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# -- The number of replicas for the service
replicaCount: 1

# -- Image configuration
image:
  # -- The repository where the image is stored
  repository: digifischdok-oci-dev.repo-ex.zcdi.dataport.de/artifactory/digifischdok-oci-dev/digifischdok/digifischdok-register-service
  # -- The policy to apply when pulling the image
  pullPolicy: Always
  # -- Overrides the image tag whose default is the chart `appVersion`.
  tag: latest

# -- Secrets used to pull images from a private registry
imagePullSecrets:
  - name: adesso-gitlab-registry

# -- Overrides for the default name of the service
nameOverride: ""
# -- Overrides for the full name of the service
fullnameOverride: ""

# -- Service account configuration
serviceAccount:
  # -- Specifies whether a service account should be created
  create: true
  # -- Whether to automatically mount the service account's API credentials
  automount: true
  # -- Annotations to add to the service account
  annotations: { }
  # -- The name of the service account to use
  # If not set and `create` is `true`, a name is generated using the fullname template
  name: ""

# -- Annotations to add to the pod
podAnnotations: { }
# -- Labels to add to the pod
podLabels: { }

# -- Security context for the pod
podSecurityContext: { }
# fsGroup: 2000

# -- Security context for the container
securityContext:
  runAsNonRoot: true
  runAsUser: 1001
  runAsGroup: 1001
  fsGroup: 1001
# capabilities:
#   drop:
#   - ALL
# readOnlyRootFilesystem: true

# -- Service configuration
service:
  # -- The type of service to create
  type: ClusterIP
  # -- The port that the service exposes
  port: 8080

# -- Ingress configuration
ingress:
  # -- Whether to enable ingress
  enabled: false
  # -- The class of the ingress
  className: ""
  # -- Annotations for the ingress
  annotations: { }
  # kubernetes.io/ingress.class: nginx
  # kubernetes.io/tls-acme: "true"
  # -- The hosts and paths for the ingress
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  # -- TLS configuration for the ingress
  tls: [ ]
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

# We usually recommend not to specify default resources and to leave this as a conscious
# choice for the user. This also increases chances charts run on environments with little
# resources, such as Minikube. If you do want to specify resources, uncomment the following
# lines, adjust them as necessary, and remove the curly braces after 'resources:'.
# -- Resource limits and requests
resources: { }
# limits:
#   cpu: 100m
#   memory: 128Mi
# requests:
#   cpu: 100m
#   memory: 128Mi

# -- Liveness probe configuration. Liveness probe is used to check if this app is responding to requests
# (after it is marked as "ready").
livenessProbe:
  # -- The HTTP path for the liveness probe
  httpGet:
    path: /api/actuator/health/liveness
    port: http
  # -- The initial delay before the liveness probe is initiated
  initialDelaySeconds: 10

# -- Readiness probe configuration. Readiness probe is used to check if this app is ready to serve traffic.
readinessProbe:
  # -- The HTTP path for the readiness probe
  httpGet:
    path: /api/actuator/health/readiness
    port: http
  # -- The initial delay before the readiness probe is initiated
  initialDelaySeconds: 60
  # -- The frequency at which the probe is performed
  periodSeconds: 1

# -- Autoscaling configuration
autoscaling:
  # -- Whether to enable autoscaling
  enabled: false
  # -- The minimum number of replicas
  minReplicas: 1
  # -- The maximum number of replicas
  maxReplicas: 100
  # -- The target CPU utilization percentage for autoscaling
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80

# -- Additional volumes on the output Deployment definition.
volumes: [ ]
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# -- Additional volumeMounts on the output Deployment definition.
volumeMounts: [ ]
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

# -- Node selector configuration
nodeSelector: { }

# -- Tolerations configuration
tolerations: [ ]

# -- Affinity configuration
affinity: { }

# -- PostgreSQL chart configuration
# Note: The PostgreSQL subchart feature has been removed. This section is kept for backward compatibility
# but has no effect. Use externalDatabase configuration instead.
postgresql:
  # -- Whether to enable the bundled PostgreSQL chart (always false as feature has been removed)
  enabled: false
  # -- Authentication details for PostgreSQL (not used as subchart feature has been removed)
  auth:
    rootPassword: postgres
    database: digifischdok
    username: digifischdok
    password: digifischdok

# -- External PostgreSQL configuration.
# These values are used to configure the database connection.
externalDatabase:
  # -- The host of the external database
  host: localhost
  # -- The port of the external database
  port: 3306
  # -- The user for the external database
  user: digifischdok
  # -- The password for the external database
  password: digifischdok
  # -- The name of the external database
  databaseName: digifischdok

# -- External Keycloak configuration
externalKeycloak:
  # -- The issuer URI for Keycloak
  issuerUri: "https://auth.dev.echolot.app/realms/digifischdok_dev"
  # -- The client ID for Keycloak
  clientId: "backend"
  # -- The client secret for Keycloak
  clientSecret: ""

# -- Spring specific configuration.
# These Properties are commonly set up in application.properties or applications.yaml files...
properties:
  # -- The active Spring profiles. (default: application.yaml; dev: application-dev.yaml).
  SPRING_PROFILES_ACTIVE: default

# -- External Mail configuration
externalMail:
  # -- The host of the external mail server
  host: "localhost"
  # -- The port of the external mail server
  port: "1025"
  # -- The user for the external mail server
  user: "none"
  # -- The password for the external mail server
  password: "none"
  # -- The from address for the external mail server
  from: "<EMAIL>"

# -- Online Service inbox configuration
osInbox:
  # -- The URI from where to fetch the auth token for the online service inbox
  tokenUri: "https://some.example.com/webidp2/connect/token"
  # -- Client ID for the inbox Keycloak
  clientId: "exampleId"
  # -- Client Secret for the inbox keylcoak
  clientSecret: "12345"
  # -- Scope of the inbox keycloak
  scope: "default"
  # -- Resource path of the inbox
  resource: "urn:dataport:osi:postfach:rz2:stage:go"
  # -- Base URN of the OSI inbox
  base-urn: "https://example.com"

cardOrder:
  # -- The URI from where to fetch the auth token for the card order service
  tokenUri: "https://some.example.com/webidp2/connect/token"
  # -- Client ID for the card order service
  clientId: "exampleId"
  # -- Client Secret for the card order service
  clientSecret: "12345"
  # -- Template version of the card order service
  templateVersion: "1.0.0"
  # -- Base URL of the card order service
  baseUrl: "https://example.com"
  # -- Endpoint path of the card order service
  endpointPath: "/api/fishing-license-order"
  # -- Days until deletion of the card order
  daysUntilDeletion: 30

# -- S3 configuration
s3:
  # -- The endpoint of the S3 bucket
  endpoint: "https://example.com"
  # -- The name of the S3 bucket
  bucket: "example-bucket"
  # -- The region of the S3 bucket
  region: "eu-central-1"
  # -- The access key ID for the S3 bucket
  accessKey: "example-access-key-id"
  # -- The secret access key for the S3 bucket
  secretKey: "example-secret-access-key"

# -- Additional Java Arguments
javaArgs: [ ]
