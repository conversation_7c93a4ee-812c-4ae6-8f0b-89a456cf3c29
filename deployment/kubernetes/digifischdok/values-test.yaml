register-service:
  imagePullSecrets:
    - name: dfd-oci-registry

  ingress:
    enabled: true
    hosts:
      - host: digifischdok-test-cluster.dsecurecloud.de
        paths:
          - path: /api
            pathType: Prefix

  externalDatabase:
    host: digifischdok-test-postgres-lb.dsc.dataport.de
    port: 5433
    user: cluster_user
    password: ${TEST_DB_PASSWORD}
    databaseName: digifischdok_test_register

  externalKeycloak:
    issuerUri: "https://digifischdok-iam.dsecurecloud.de:8443/realms/digifischdok_dev"
    clientId: "backend-test"
    clientSecret: ${KC_CLIENT_SECRET_TEST}

  externalMail:
    host: ************
    port: 25
    user: <EMAIL>
    password: none
    from: <EMAIL>

  osInbox:
    tokenUri: "https://idp.serviceportal-stage.gemeinsamonline.de/webidp2/connect/token"
    clientId: "urn:digifischdok:stage"
    clientSecret: "${INBOX_CLIENT_SECRET_TEST}"
    clientScope: "access_urn:dataport:od:digifischdok:stage:go:DigiFischDok,default"
    resource: "urn:dataport:osi:postfach:rz2:stage:go"
    baseUrn: "https://api-gateway-stage.dataport.de:443"

  cardOrder:
    tokenUri: "https://digifischdok-iam.dsecurecloud.de:8443/realms/license_print_mock/protocol/openid-connect/token"
    clientId: "CardproviderService"
    clientSecret: "${CARD_ORDER_CLIENT_SECRET}"
    templateVersion: "1.0.0"
    baseUrl: "https://digifischdok-license-print.dsecurecloud.de"
    endpointPath: "/api/fishing-license-order"
    daysUntilDeletion: 30

  s3:
    endpoint: https://dstoragecloud.dataport.de:9021
    bucket: dsec-kd9pvjm7cgg89y4dsayubhpqdyc
    region: eu-central-1
    accessKey: ${S3_ACCESS_KEY}
    secretKey: ${S3_SECRET_KEY}

  properties:
    SPRING_PROFILES_ACTIVE: default,dev

  javaArgs:
    - "-Dhttp.proxyHost=${DATAPORT_PROXY_HOST}"
    - "-Dhttp.proxyPort=${DATAPORT_PROXY_PORT}"
    - "-Dhttps.proxyHost=${DATAPORT_PROXY_HOST}"
    - "-Dhttps.proxyPort=${DATAPORT_PROXY_PORT}"
    - "-Dhttp.nonProxyHosts=localhost|*.dsc.dataport.de|dstoragecloud.dataport.de"

  resources:
    requests:
      cpu: 1000m
      memory: 4Gi
    limits:
      cpu: 2000m
      memory: 4Gi

  networkPolicy:
    enabled: true
    podSelector:
      matchLabels:
        app.kubernetes.io/name: register-service
    ingress:
      - from:
          - namespaceSelector:
              matchLabels:
                kubernetes.io/metadata.name: kube-system
            podSelector:
              matchLabels:
                app.kubernetes.io/component: controller
                app.kubernetes.io/name: rke2-ingress-nginx
        ports:
          - port: 8080
            protocol: TCP
    egress:
      - ports:
          - port: 3128
            protocol: TCP
          - port: 5433
            protocol: TCP
          - port: 25
            protocol: TCP
          - port: 9021
            protocol: TCP
        to:
          - ipBlock:
              cidr: 10.0.0.0/8
      - ports:
          - port: 53
            protocol: UDP
        to:
          - namespaceSelector:
              matchLabels:
                kubernetes.io/metadata.name: kube-system
            podSelector:
              matchLabels:
                k8s-app: kube-dns

web-app:
  imagePullSecrets:
    - name: dfd-oci-registry

  ingress:
    enabled: true
    hosts:
      - host: digifischdok-test-cluster.dsecurecloud.de
        paths:
          - path: /
            pathType: Prefix

  properties:
    API_URL: https://digifischdok-test-cluster.dsecurecloud.de/api
    WEB_URL: https://digifischdok-test-cluster.dsecurecloud.de
    KEYCLOAK_URL: https://digifischdok-iam.dsecurecloud.de:8443
    KEYCLOAK_CLIENT_ID: frontend-test
    KEYCLOAK_REALM: digifischdok_dev
    SELF_SERVICE_PASSWORD_CHANGE_LINK: https://pws.dataport.de/request-password-change
    ENVIRONMENT: test

  resources:
    requests:
      cpu: 100m
      memory: 128Mi
    limits:
      cpu: 1000m
      memory: 128Mi

  networkPolicy:
    enabled: true
    defaultDenyEgress: true
    podSelector:
      matchLabels:
        app.kubernetes.io/name: web-app
    ingress:
      - from:
          - namespaceSelector:
              matchLabels:
                kubernetes.io/metadata.name: kube-system
            podSelector:
              matchLabels:
                app.kubernetes.io/component: controller
                app.kubernetes.io/name: rke2-ingress-nginx
        ports:
          # Use port 8080 because rootless nginx cannot run on port 80
          - port: 8080
            protocol: TCP